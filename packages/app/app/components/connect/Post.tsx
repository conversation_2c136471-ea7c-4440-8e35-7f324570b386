import React, { memo, useState, useCallback } from "react";
import LogoSimple from "@assets/svg/LogoOutline";
import Comment from "@assets/svg/connect/Comment";
import Like from "@assets/svg/connect/Like";
import ThreeDots from "@assets/svg/connect/ThreeDots";
import AppText from "@components/common/AppText";
import { imageKit } from "@utils/index";
import { format, parseISO } from "date-fns";
import {
  View,
  StyleSheet,
  Image,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { Image as CacheImage } from "react-native-expo-image-cache";
import { RouterOutput } from "../../../../shared/index";
import { Globe2, HelpingHand, Lock } from "lucide-react-native";
import Pressable from "@components/common/Pressable";
import ReactionPicker from "./ReactionPicker";
import ContextMenuModal from "@components/common/ContextMenuModal";
import { Share } from "react-native";
import { ENV } from "@constants/index";
import { Clipboard } from "react-native";
import { EmojiContextMenuModal } from "@components/common/EmojiContextMenuModal";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";
import { PostOrGroupCard } from "@components/common/PostOrGroupCard";
import ChatMessage from "@screens/rabbles/ChatMessge";

export type Post = RouterOutput["connect"]["getPosts"]["posts"][number];
type ConnectProps = Post & {
  onPress?: (post: Partial<Post>) => void;
  onLike: (post: Partial<Post>, status: boolean) => void;
  isLikeLoading?: boolean;
  onOptionsSelect?: (post: Partial<Post>) => void;
  setDeleteModalVisible?: (v: any) => void;
  setSelectedPost?: (v: any) => void;
  showInComments?: boolean;
  isAutoGroupCreated?: boolean;
};

const { width } = Dimensions.get("screen");
const MARGIN = 16;
const postImageWidth = width - 2 * MARGIN;
const postImageHeight = (postImageWidth / 4) * 3;

const DEFAULT_REACTIONS = ["❤️", "😂", "😮", "😢", "👍"];

export default memo(function ConnectPost(props: ConnectProps) {
  const [showEmojiContextMenuModal, setShowEmojiContextMenuModal] =
    useState(false);
  const [showContextMenu, setShowContextMenu] = useState(true);
  const [selectedReaction, setSelectedReaction] = useState<string | null>(null);

  const handleCloseModal = () => {
    setShowEmojiContextMenuModal(false);
  };

  const handleReactionSelect = (emoji: string) => {
    setSelectedReaction(emoji);
    console.log({ testEmoigi: emoji });
    onLike(props, true, groupName, emoji);
  };

  const {
    createdAt,
    isLiked,
    reactionType,
    recentLikes,
    totalLikes,
    user,
    title,
    image,
    onPress,
    onLike,
    onOptionsSelect,
    isLikeLoading: likeStatus,
    rabbleGroup,
    setDeleteModalVisible,
    setSelectedPost,
    showInComments = false,
    isAutoGroupCreated = false,
  } = props;
  const groupName = rabbleGroup?.at(0)?.groupName;
  const groupPrivacy = rabbleGroup?.at(0)?.privacy;

  const recentlyLikedUsername =
    recentLikes?.[0]?.userDocument?.[0]?.username ||
    recentLikes?.[0]?.userDocument?.[0]?.firstname;

  const { user: contextUser } = useSession();

  const handlePress = () => onPress?.(props);
  const handleLike = () => onLike(props, isLiked, groupName, selectedReaction);
  const handlePostAction = () => onOptionsSelect?.(props);
  // console.log({ groupName, user })
  return (
    <>
      <Pressable
        onLongPress={() => {
          setShowEmojiContextMenuModal(true);
          setShowContextMenu(true);
        }}
      >
        {showInComments ? (
          <ChatMessage
            avatar={image}
            name={user?.[0]?.username || user?.[0]?.firstname || ""}
            message={title || ""}
            time={format(new Date(createdAt), "MMM dd yyyy, hh:mm a")}
            reactions={[{ emoji: selectedReaction || reactionType }]}
            isOwn={
              isAutoGroupCreated ? true : user?.[0]?._id === contextUser?._id
            }
          />
        ) : (
          <PostOrGroupCard
            onPress={handlePress}
            name={
              groupName ||
              user?.[0]?.username ||
              `${user?.[0]?.firstname || ""} ${user?.[0]?.lastname || ""}` ||
              ""
            }
            imageUrl={image || ""}
            message={title || ""}
            date={format(new Date(createdAt), "dd-MMM-yy")}
          />
        )}
      </Pressable>
      {/* <View
        className="mt-6 rounded-xl h-auto bg-white my-4 relative overflow-visible"
        style={styles.container}
      >
        <Pressable
          onPress={handlePress}
          onLongPress={() => {
            setShowEmojiContextMenuModal(true);
            setShowContextMenu(true);
          }}
          actionTag="profile-pic"
        >
          <View className="flex-row p-2 items-center">
            <View>
              {!user?.[0]?.profilePicture ? (
                <LogoSimple width={36} height={36} />
              ) : (
                <Image
                  className="h-9 w-9 rounded-full "
                  source={{
                    uri: imageKit({
                      imagePath: user?.[0]?.profilePicture,
                      transform: ["w-600"],
                    }),
                  }}
                />
              )}
            </View>

            <View className="mx-3 flex-grow">
              <AppText className="text-sm font-montserratMedium text-black">
                {user?.[0]?.username || user?.[0]?.firstname}
              </AppText>
              {!!(groupName && groupPrivacy) && (
                <View className="flex-row items-center gap-1">
                  {groupPrivacy === "private" ? (
                    <Lock color="#acacac" size={16} />
                  ) : (
                    <Globe2 color="#acacac" size={16} />
                  )}
                  <AppText className="text-xs">{groupName}</AppText>
                </View>
              )}
              <AppText className="text-[10px] font-montserrat text-[#76777E] leading-3 font-montserratMedium">
                {createdAt &&
                  format(new Date(createdAt), "MMM dd, yyyy, hh:mm a")}
              </AppText>
            </View>
            {onOptionsSelect && (
              <Pressable
                actionTag="post options"
                className="items-end justify-end mr-4 mb-2 z-1"
                onPress={handlePostAction}
              >
                <ThreeDots />
              </Pressable>
            )}
          </View>
          <View className="">
            <AppText className="mx-3 mb-3 text-sm">{title}</AppText>
            {image && (
              <>
                <CacheImage
                  uri={imageKit({
                    imagePath: image,
                    transform: ["w-600"],
                  })}
                  preview={{
                    uri: imageKit({
                      imagePath: image,
                      transform: ["w-30", "bl-6"],
                    }),
                  }}
                  tint="dark"
                  transitionDuration={300}
                  style={{
                    height: postImageHeight,
                    width: "100%",
                  }}
                />
              </>
            )}
          </View>
        </Pressable>
        {!!recentLikes?.length && (
          <View className="ml-3 my-3 flex-row items-center ">
            <View className="flex items-center flex-row ">
              {recentLikes?.map((_, idx) => {
                if (_.userDocument?.[0]?.profilePicture)
                  return (
                    <Image
                      key={idx}
                      source={{
                        uri: imageKit({
                          imagePath: _.userDocument?.[0]?.profilePicture,
                          transform: ["w-100"],
                        }),
                      }}
                      className="h-5 w-5 rounded-full "
                      style={styles.likeImageStyles}
                    />
                  );
                return (
                  <View key={idx} style={{ zIndex: idx, left: idx * -7 }} />
                );
              })}
            </View>

            <AppText className="ml-2 text-[12px]">
              {recentLikes.length == 1
                ? `Liked by ${recentlyLikedUsername}`
                : recentLikes.length === 0
                ? ""
                : `Liked by ${recentlyLikedUsername} and ${
                    totalLikes - 1
                  } others`}
            </AppText>
          </View>
        )}
        <View className="border-[0.5px] border-slate-200" />
        <View className="my-4 justify-around flex-row relative">
          {likeStatus ? (
            <ActivityIndicator color="#f0304e" />
          ) : (
            <Pressable
              onPress={handleLike}
              onLongPress={() => {
                setShowContextMenu(false);
                setShowEmojiContextMenuModal(true);
              }}
              hitSlop={10}
              actionTag="post like"
              className="relative"
            >
              {selectedReaction || (reactionType && isLiked) ? (
                <AppText className="text-xl">
                  {selectedReaction || reactionType}
                </AppText>
              ) : (
                <Like active={isLiked} />
              )}
            </Pressable>
          )}
          <Pressable
            onPress={handlePress}
            hitSlop={10}
            actionTag="post comment"
            className="relative"
          >
            <Comment className="mt-1" />
          </Pressable>
        </View>
      </View> */}

      <EmojiContextMenuModal
        visible={showEmojiContextMenuModal}
        showContextMenu={showContextMenu}
        onClose={handleCloseModal}
        onReactionSelect={handleReactionSelect}
        contextMenuItems={[
          {
            label: "Reply",
            icon: "➡️",
            onClick: handlePress,
          },
          {
            label: "Forward",
            icon: "⏩",
            onClick: () => {
              mixpanel.trackEvent(
                "Post forwarded",
                {
                  email: contextUser?.email || "",
                  phone: contextUser?.contact?.phone || "",
                  post_id: props?._id,
                  forwarded_post_content: props?.title,
                },
                String(contextUser?._id),
                "v2"
              );
              Share.share({
                message: props?.title,
              });
            },
          },
          {
            label: "Copy",
            icon: "📃",
            onClick: () => {
              mixpanel.trackEvent(
                "Post copied",
                {
                  email: contextUser?.email || "",
                  phone: contextUser?.contact?.phone || "",
                  post_id: props?._id,
                  copy_post_content: props?.title,
                },
                String(contextUser?._id),
                "v2"
              );
              Clipboard.setString(title);
            },
          },
          {
            label: "Star",
            icon: "⭐️",
            onClick: () => {},
          },
          ...(contextUser?._id?.toString() === props?.user[0]?._id?.toString()
            ? [
                {
                  label: "Delete",
                  icon: "🗑",
                  color: "#E81448",
                  onClick: () => {
                    if (setDeleteModalVisible && setSelectedPost) {
                      setSelectedPost(props);
                      setDeleteModalVisible(true);
                      mixpanel.trackEvent(
                        "Post Deleted",
                        {
                          email: contextUser?.email || "",
                          phone: contextUser?.contact?.phone || "",
                          post_id: props?._id,
                          post_content: props?.title,
                        },
                        String(contextUser?._id),
                        "v2"
                      );
                    }
                  },
                },
              ]
            : []),
          {
            label: "More...",
            icon: "",
            color: "#0B79D3",
            onClick: () => {},
          },
        ]}
        selectedCard={
          <ChatMessage
            avatar={image}
            name={user?.[0]?.username || user?.[0]?.firstname || ""}
            message={title || ""}
            time={format(new Date(createdAt), "MMM dd yyyy, hh:mm a")}
            reactions={[{ emoji: selectedReaction || reactionType }]}
            isOwn={user?.[0]?._id === contextUser?._id}
            isFromContextMenu
          />
        }
      />
    </>
  );
});

const styles = StyleSheet.create({
  container: {
    shadowColor: "#004987",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  likeImageStyles: { borderWidth: 2, borderColor: "#fff" },
});
