import { View } from "react-native";
import NotificationIcon from "@assets/svg/Notification";
import { useNavigation } from "@react-navigation/native";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { StackNavigationProp } from "@react-navigation/stack";
import MyRabblesLogoHeader from "@assets/svg/MyRabblesLogoHeader";
import useAuthGuard from "@hooks/useAuthGuard";
import { trpc } from "@providers/RootProvider";
import { StyleSheet } from "react-native";
import { useEffect } from "react";
import { useAppProvider } from "../../providers/AppProvider";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";
import Svg, { Circle, Path, SvgProps } from "react-native-svg";


type Props = {
  headerClasses?: string;
  showThreeDots?: boolean;
};


const ThreeDots = (props: SvgProps) => (
  <Svg
    // xmlns="http://www.w3.org/2000/svg"
    width={30}
    height={30}
    viewBox="0 0 30 30"
    fill="none"
    {...props}
  >
    <Circle cx={15} cy={15} r={15} fill="#DFE1E2" />
    <Circle cx={10.5} cy={15.5} r={1.5} fill="#0B79D3" />
    <Circle cx={15.5} cy={15.5} r={1.5} fill="#0B79D3" />
    <Circle cx={20.5} cy={15.5} r={1.5} fill="#0B79D3" />
  </Svg>
);

export default function Header(props: Props) {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const authGuard = useAuthGuard();
  const { user } = useSession();
  const { notficationCount, refetchNotificationCount } = useAppProvider();

  useEffect(() => {
    (async () => {
      refetchNotificationCount();
    })();
  }, []);
  return (
    <View
      className={[
        "relative flex-row justify-between items-center mt-2",
        props.headerClasses,
      ].join(" ")}
    >
     
      {props.showThreeDots ? (
          <ThreeDots onPress={() => alert("Coming Soon")} />
      ) : (
      <View className="">
        <MyRabblesLogoHeader />
      </View>
      )}
      <View className="relative">
        <NotificationIcon
          onPress={() => {
            mixpanel.trackEvent(
              "Notifications checked",
              {
                email: user?.email || "",
                phone: user?.contact?.phone || "",
              },
              String(user?._id),
              "v2"
            );
            authGuard(() => navigation.navigate("NotificationScreen"));
          }}
        />
        {!!notficationCount && (
          <View
            style={styles.notificationDot}
            className="absolute right-0 top-0 w-[12px] h-[12px] rounded-lg"
          ></View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  notificationDot: {
    shadowColor: "#004987",
    backgroundColor: "red",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});
