import ButterFlyIcon from "@assets/svg/ButterFlyIcon";
import QuestionPrompt from "@assets/svg/QuestionPrompt";
import ProgressBar from "@components/common/ProgressBar";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";
import React, { useState } from "react";
import { Dimensions, View, Text, Platform } from "react-native";
import BookIcon from "@assets/svg/BookIcon";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { RootNavigationProp } from "@navigation/root-navigator";
import Pressable from "@components/common/Pressable";
import withPressAnimation from "@components/common/AnimateButton";
import AppFormInput from "@components/form/AppFormInput";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import errorHandler from "@utils/errorhandler";
import { zodResolver } from "@hookform/resolvers/zod";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import AppScrollView from "@components/common/AppScrollView";
import AppButton from "@components/common/AppButton";
import Svg, { Path, Ellipse, SvgProps, G, Rect, Defs } from "react-native-svg";
import useImagePicker from "@hooks/useImagePicker";
import { Image } from "react-native";
import { imageKit } from "@utils/index";
import { s3Paths } from "@constants/index";
import { useUploadImage } from "@services/upload";
import { RABBLEGROUP_TYPES } from "../../../../shared/validators/rabblegroup.validator";
import mixpanel from "@utils/mixpanel";
import clsx from "clsx";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { useAppProvider } from "@providers/AppProvider";

function PhotoIcon(props: SvgProps) {
  return (
    <Svg width={94} height={91} viewBox="0 0 94 91" fill="none" {...props}>
      <G filter="url(#filter0_d_10483_20108)">
        <Rect x={4} width={85.195} height={83} rx={10} fill="#E1F1FF" />
      </G>
      <Path
        d="M77.485 41.667c0 16.656-13.647 30.167-30.493 30.167-16.845 0-30.492-13.511-30.492-30.167C16.5 25.011 30.147 11.5 46.992 11.5c16.846 0 30.493 13.511 30.493 30.167z"
        fill="#fff"
        stroke="#B4DDFF"
      />
      <Path
        d="M58.582 40.036c2.698-.375 4.775-2.663 4.78-5.436 0-2.733-2.013-4.998-4.652-5.427M62.142 46.451c2.613.387 4.436 1.292 4.436 3.158 0 1.284-.858 2.118-2.247 2.642"
        stroke="#004987"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M46.977 47.245c-6.216 0-11.524.932-11.524 4.654 0 3.72 5.275 4.679 11.524 4.679 6.215 0 11.522-.923 11.522-4.647 0-3.723-5.274-4.686-11.522-4.686zM46.977 41.932c4.079 0 7.386-3.27 7.386-7.308 0-4.035-3.307-7.308-7.386-7.308-4.078 0-7.385 3.273-7.385 7.308-.016 4.023 3.266 7.295 7.331 7.308h.054z"
        fill="#004987"
      />
      <Path
        d="M35.37 40.036c-2.7-.375-4.775-2.663-4.78-5.436 0-2.733 2.012-4.998 4.652-5.427M31.81 46.451c-2.613.387-4.436 1.292-4.436 3.158 0 1.284.858 2.118 2.247 2.642"
        stroke="#004987"
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Defs></Defs>
    </Svg>
  );
}

export function PlaceholderLogo(props: SvgProps) {
  return (
    <Svg width={30} height={23} viewBox="0 0 30 23" fill="none" {...props}>
      <Path
        d="M9.682 22.7c-.11 0-.219-.007-.327-.023-.437-.069-.533-.291-.547-.417-.045-.327.304-.678 1.067-1.075-1.985.873-3.977 1.2-5.766.928-2.831-.437-3.699-2.62-3.964-3.889-.448-2.14.183-4.745 1.325-5.466.502-.317 2.12-.736 3.406-.858-.015-.02-.032-.04-.046-.06-.538-.684-.959-1.473-1.125-2.109-.508-1.924 1.794-8.233 1.892-8.5C5.614 1.184 6.02.139 6.88.013c.612-.09 1.263.303 1.936 1.174.797 1.032 6.7 8.84 7.21 14.431a9.979 9.979 0 00.888-1.55c1.921-4.234.532-8.477-1.41-11.034-.577-.764-1.061-1.125-1.398-1.047-.702.16-1.12 2.224-1.426 3.731a.217.217 0 01-.094.139.219.219 0 01-.335-.226c.457-2.253.85-3.862 1.758-4.07.539-.123 1.124.261 1.843 1.208 2.017 2.659 3.46 7.07 1.46 11.48a10.937 10.937 0 01-1.258 2.065v.232a3.46 3.46 0 01.713-.505c.765-1.579 2.531-2.742 4.79-3.106.89-.144 1.325-.076 1.452.225.167.393-.412.873-.669 1.07a.22.22 0 01-.242.012.22.22 0 01-.104-.219.217.217 0 01.085-.143c.245-.183.481-.437.52-.546-.063-.026-.285-.08-.973.03-1.966.316-3.525 1.263-4.3 2.557h.037c.307.046.568.27.796.685a.18.18 0 01.015.032c.219.61.1 1.16-.305 1.47a.899.899 0 01-1.048.057c-.181-.122-.57-.506-.317-1.433-.154.125-.32.274-.497.437a4.762 4.762 0 01-.426 1.483c-1.353 2.793-4.39 4.048-5.899 4.048zm-.437-.51c.056.037.281.11.801.046 1.22-.155 3.867-1.14 5.145-3.766.119-.247.211-.505.276-.77-1.147 1.11-2.712 2.619-5.07 3.71-.96.453-1.128.72-1.154.781h.002zm-4.013-9.878c-.355.023-.71.063-1.06.123-1.066.17-2.106.461-2.473.693C.81 13.69.122 16 .57 18.136.81 19.292 1.6 21.29 4.174 21.682c2.314.353 4.997-.353 7.552-1.989a15.681 15.681 0 003.891-3.515c-.05-1.837-.793-4.2-2.21-6.985C11.48 5.416 8.953 2.084 8.47 1.454 7.91.73 7.383.38 6.94.444c-.6.09-.935.93-.938.938-.023.063-2.353 6.446-1.882 8.24.154.588.546 1.325 1.063 1.972.08.102.156.192.23.274.455-.01.826.035 1.027.16a.415.415 0 01.219.404c-.037.416-.3.483-.41.492-.313.027-.698-.28-1.017-.612zm11.86 4.069a3.798 3.798 0 00-.159.45c-.133.467-.083.832.135.982a.466.466 0 00.536-.042c.244-.187.305-.545.165-.962-.155-.277-.31-.424-.47-.448a.478.478 0 00-.206.02zm-1.498.512c-.146.174-.297.345-.454.514l.055-.054c.137-.133.268-.26.393-.377l.006-.083zm-9.709-4.575c.************.32.171a.287.287 0 00.019-.085.767.767 0 00-.339-.086zM24.97 4.54l.556 1.646a1 1 0 00.822.672l.892.113-1.285.995a1 1 0 00-.327.448l-.658 1.8-.657-1.8a1 1 0 00-.328-.448L22.7 6.971l.892-.113a1 1 0 00.822-.672l.556-1.647z"
        fill="#FF8E1C"
      />
      <Ellipse
        cx={27.8075}
        cy={11.9173}
        rx={1.7025}
        ry={1.7025}
        fill="#FF8E1C"
      />
    </Svg>
  );
}

const AnimatedAppButton = withPressAnimation(AppButton);

const screenWidth = Dimensions.get("window").width;
const cardWidth = screenWidth * 0.9;

const schema = z.object({
  groupName: z.string().min(1, "Name is required"),
  groupDescription: z.string().min(1, "Description is required"),
  groupGuidelines: z.string(),
});

type Form = z.infer<typeof schema>;

export default function CreateNewRabble() {
  const navigation = useNavigation<RootNavigationProp>();
  const { params } = useRoute<RouteProp<RootNavParams, "CreateNewRabble">>();

  const [logo, setLogo] = useState("");

  const { user } = useSession();
  const { images, imagePicker } = useImagePicker();
  const uploadImage = useUploadImage();
  let { groupPayload, setGroupPayload } = useAppProvider();

  const { mutateAsync: createRabbleGroup } =
    trpc.rabbleGroups.createRabbleGroup.useMutation();

  const methods = useForm<Form>({
    resolver: zodResolver(schema),
    defaultValues: {
      groupName: "",
      groupDescription: "",
      groupGuidelines: "",
    },
  });

  const uploadImageIfPresent = async (logo: string) => {
    if (logo) {
      const formdata = new FormData();
      formdata.append("filePath", s3Paths.post);

      formdata.append("image", {
        uri: logo,
        type: "image/png",
        name: "image.png",
      } as any);

      // upload image
      const {
        data: { s3Path },
      } = await uploadImage.mutateAsync({ formdata });
      return { image: s3Path };
    }
    return {};
  };

  const handleSelectImage = async () => {
    const assets = await imagePicker({
      allowsEditing: true,
      quality: 0.4,
      aspect: [1, 1],
    });

    if (assets?.length) {
      const selectedImage = assets[0].uri;
      setLogo(selectedImage);
    }
  };
  console.log("createRabble called");
  const { handleSubmit, formState } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      const imageData = await uploadImageIfPresent(logo);
      // const createdGroup = await createRabbleGroup({
      //   groupDescription: data?.groupDescription || "",
      //   groupName: data?.groupName || "",
      //   groupGuidelines: data?.groupGuidelines || "",
      //   privacy: RABBLEGROUP_TYPES.public,
      //   ...imageData,
      // });
      setGroupPayload({
        groupDescription: data?.groupDescription || "",
        groupName: data?.groupName || "",
        groupGuidelines: data?.groupGuidelines || "",
        ...imageData,
      });
      mixpanel.trackEvent(
        "Rabble community created (Step 2)(Inviting)",
        {
          community_name: data?.groupName,
          community_desc: data?.groupDescription,
          community_guideline: data?.groupGuidelines,
          email_or_phone: user?.email || user?.contact?.phone || "",
        },
        user?._id?.toString(),
        "v2"
      );
      navigation.navigate("GroupPrivacyPreferences", {
        // groupId: createdGroup?._id,
        fromConnectScreen: params?.fromConnectScreen,
      });
    } catch (ex) {
      errorHandler(ex);
    }
  });

  return (
    <Screen>
      <AppScrollView>
        <View className="flex-row items-center mt-8">
          <ChevronLeft
            color={"#004987"}
            size={22}
            className="ml-4"
            onPress={() => navigation.goBack()}
          />
          <ProgressBar
            total={3}
            current={1}
            style={{ marginLeft: 8, flex: 1, marginRight: 16 }}
          />
        </View>
        <View className="flex-1 items-center mt-[-12px]">
          <View className="flex-row items-center">
            <ButterFlyIcon height={140} width={90} />
            <QuestionPrompt message="Let’s build a      rabble!" />
          </View>
          <View
            className={clsx(
              "shadow-md  rounded-xl",
              Platform.OS === "ios" && "shadow-md  rounded-xl"
            )}
          >
            <Pressable
              actionTag="select group image"
              onPress={handleSelectImage}
            >
              <PhotoIcon />
              {images?.length && (
                <Image
                  className="absolute left-0 right-0 top-0 bottom-0 h-[85px] "
                  source={{
                    uri: images?.length
                      ? images.at(0)?.uri
                      : imageKit({
                          imagePath: "",
                          transform: ["w-500"],
                        }),
                  }}
                />
              )}
            </Pressable>
          </View>
          {!images?.length && (
            <Text className="mb-4 text-[#336D9F] text-center font-[Montserrat] text-[12px] font-bold leading-[130%]">
              {"Add Photo"}
            </Text>
          )}
          <View
            className="flex-1 p-2 mt-[-12px]"
            style={{ width: cardWidth, gap: 8 }}
          >
            <FormProvider {...methods}>
              <AppFormInput
                name="groupName"
                fieldClass=""
                placeholderTextColor="#B4DDFF"
                placeholder="Community name"
                borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                className="rounded-lg text-[18px]"
              />
              <AppFormInput
                // label="description"
                placeholder="Community Description"
                iconLeft={
                  <View className="-mt-12">
                    <PlaceholderLogo />
                  </View>
                }
                placeholderTextColor="#B4DDFF"
                name="groupDescription"
                multiline
                borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF] "
                className="h-32 text-[18px] "
                style={{ textAlignVertical: "top" }}
              />

              <AppFormInput
                // label="description"
                placeholder="Community Guidelines"
                iconLeft={
                  <View className="-mt-12">
                    <PlaceholderLogo />
                  </View>
                }
                placeholderTextColor="#B4DDFF"
                name="groupGuidelines"
                multiline
                borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF]"
                className="h-32 text-[18px]"
                style={{ textAlignVertical: "top" }}
              />
            </FormProvider>
          </View>
        </View>
      </AppScrollView>
      <View className="flex flex-row items-center justify-between mb-2 mx-4">
        <AnimatedAppButton
          btnContainer="flex flex-row p-1"
          title="CONTINUE"
          variant={formState.isValid ? "new-primary" : "disabled"}
          className="flex-1 rounded-full"
          textClassName="text-[21px]"
          disabled={!formState?.isValid}
          style={
            formState?.isValid
              ? {
                  shadowColor: "#003366",
                  shadowOffset: { width: 0, height: 3 },
                  shadowOpacity: 1,
                  shadowRadius: 1,
                  elevation: 5,
                }
              : {}
          }
          onPress={onSubmit}
        />
      </View>
    </Screen>
  );
}
