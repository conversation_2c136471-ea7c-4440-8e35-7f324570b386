export type PostTypes = "CONNECT" | "PRIVATE" | "PUBLIC";

export interface ConnectUser {
  _id: string;
  externalId: string;
  createdAt: string;
  email: string;
  firstname: string;
  lastname: string;
  username: string;
  profilePicture?: string;
  rabbleGroup?: string;
  postType?: PostTypes;
}
export interface RecentLikes {
  _id: string;
  post: string;
  createdAt: string;
  userDocument: ConnectUser[];
}

export interface Post {
  _id: string;
  title?: string;
  image?: string;
  createdAt: string;
  updatedAt: string;
  recentLikes: RecentLikes[];
  totalLikes: number;
  isLiked: boolean;
  user: ConnectUser[];
}

export interface Comment {
  _id: string;
  comment: string;
  post: string;
  likes: number;
  isLiked: boolean;
  user: ConnectUser;
  createdAt: string;
  repliedTo?: any;
}
