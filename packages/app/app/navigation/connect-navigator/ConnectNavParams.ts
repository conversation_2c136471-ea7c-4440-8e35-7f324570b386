import { RabbleGroup, RabbleGroupUserRole } from "../../models";

export type ConnectNavParams = {
  ConnectPosts: undefined;
  RabbleGroups: { selectedTab?: any | undefined };
};

export type ConnectPostNavParams = {
  ConnectScreen: undefined;
  RabbleGroups: { selectedTab?: any | undefined };
  ManageHomeScreen?: { startOfWeek?: string; topicId?: string };
  CreateNewRabble?: { fromConnectScreen?: boolean };
  GroupPrivacyPreferences?: { fromConnectScreen?: boolean };
  RabbleTagAssociation?: { fromConnectScreen?: boolean };
  OnboardingTopics?: { fromManageScreen?: boolean | any };
  OnboardingQuestion: {
    topicId?: any;
    currentIndex?: number;
    questionAnswer?: any;
    fromManageScreen?: boolean | any;
  };
  ManageStreakScreen?: {
    streakCount?: number | string;
    dailyTrackers?: any;
    isDailyCheckInCompleted?: any;
  };
  RabbleDetailsScreenV2: RabbleGroup;
  PostOrGroupPostDetailsScreen: {
    postId: string;
    postType: "CONNECT" | "PRIVATE" | "PUBLIC";
    groupId?: string;
    groupRole?: RabbleGroupUserRole;
  };
};

export type RabbleGroupsNavParams = {
  RabbleGroupsScreen: undefined;
};
