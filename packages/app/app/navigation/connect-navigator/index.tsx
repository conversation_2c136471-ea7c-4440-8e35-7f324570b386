import NavigationHeader from "@components/navigation/NavigationHeader";
import { Entypo } from "@expo/vector-icons";
import {
  createStackNavigator,
  StackNavigationOptions,
} from "@react-navigation/stack";

import Connect from "@screens/connect/ConnectPosts";
import {
  ConnectNavParams,
  ConnectPostNavParams,
  RabbleGroupsNavParams,
} from "./ConnectNavParams";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import RabbleGroups from "@screens/rabbles/RabbleGroups";
import { SafeAreaView } from "react-native";
import Screen from "@components/common/Screen";
import CreateNewRabble from "@components/onboarding/CreateNewRabble";
import GroupPrivacyPreferences from "@components/onboarding/GroupPrivacyPreferences";
import RabbleTagAssociation from "@components/onboarding/RabbleTagAssociation";
import ManageStreakScreen from "@screens/manage/ManageStreak";
import OnboardingTopics from "@components/onboarding/OnboardingTopics";
import ManageHomeScreen from "@screens/manage/ManageHomeScreen";
import RabbleDetailsScreenV2 from "@screens/rabbles/RabbleDetailsScreenV2";
import PostOrGroupPostDetailsScreen from "@screens/rabbles/PostOrGroupPostDetailsScreen";
import OnboardingQuestion from "@components/onboarding/OnboardingQuestions";

const options: StackNavigationOptions = {
  // header: NavigationHeader,
  // headerLeft: () => (
  //   <Entypo name="chevron-small-left"  color="black" />
  // ),
  headerShown: false,
  cardStyle: { backgroundColor: "#F5F7F9" },
};

const ConnectStack = createStackNavigator<ConnectPostNavParams>();
function ConnectPostsNav() {
  return (
    <ConnectStack.Navigator screenOptions={{ ...options }}>
      <ConnectStack.Screen name="ConnectScreen" component={Connect} />
    </ConnectStack.Navigator>
  );
}

const RabbleGroupStack = createStackNavigator<RabbleGroupsNavParams>();
function RabbleGroupsNav() {
  return (
    <RabbleGroupStack.Navigator screenOptions={{ ...options }}>
      <RabbleGroupStack.Screen
        name="RabbleGroupsScreen"
        component={RabbleGroups}
      />
    </RabbleGroupStack.Navigator>
  );
}

const Tab = createMaterialTopTabNavigator<ConnectNavParams>();
export default function ConnectNavigator() {
  return (
    <ConnectStack.Navigator screenOptions={{ ...options }}>
      {/* <Screen headerClasses="mx-4"> */}
      {/* <Tab.Navigator
        screenOptions={{
          tabBarActiveTintColor: "#FF8E1C",
          tabBarAndroidRipple: { color: "#FF8E1C50", borderless: true },
          tabBarIndicatorStyle: { backgroundColor: "#FF8E1C" },
          tabBarStyle: { elevation: 0 },
          tabBarLabelStyle: {
            fontFamily: "Montserrat_500Medium",
            textTransform: "none",
            fontSize: 16,
          },
        }}
      > */}
      {/* <Tab.Screen
          name="ConnectPosts"
          component={ConnectPostsNav}
          options={{
            tabBarLabel: "Posts",
          }}
        /> */}
      <ConnectStack.Screen
        name="RabbleGroups"
        component={RabbleGroupsNav}
        options={
          {
            // tabBarLabel: "Rabbles",
          }
        }
      />
      <ConnectStack.Screen
        name="CreateNewRabble"
        component={CreateNewRabble}
        options={{ gestureEnabled: false }}
      />
      <ConnectStack.Screen
        name="RabbleDetailsScreenV2"
        component={RabbleDetailsScreenV2}
        options={{ gestureEnabled: false }}
      />
      <ConnectStack.Screen
        name="PostOrGroupPostDetailsScreen"
        component={PostOrGroupPostDetailsScreen}
        options={{ gestureEnabled: false }}
      />
      <ConnectStack.Screen
        name="GroupPrivacyPreferences"
        component={GroupPrivacyPreferences}
        options={{ gestureEnabled: false }}
      />
      <ConnectStack.Screen
        name="RabbleTagAssociation"
        component={RabbleTagAssociation}
        options={{ gestureEnabled: false }}
      />
      <ConnectStack.Screen
        name="ManageStreakScreen"
        component={ManageStreakScreen}
        options={{ headerShown: false }}
      />
      <ConnectStack.Screen
        name="OnboardingTopics"
        component={OnboardingTopics}
        options={{ headerShown: false }}
        initialParams={{ hideTabBar: true }}
      />
      <ConnectStack.Screen
        name="ManageHomeScreen"
        component={ManageHomeScreen}
        options={{ headerShown: false }}
      />
       <ConnectStack.Screen
        name="OnboardingQuestion"
        component={OnboardingQuestion}
        options={{ headerShown: false }}
      />
      {/* </Tab.Navigator> */}
      {/* </Screen> */}
    </ConnectStack.Navigator>
  );
}
