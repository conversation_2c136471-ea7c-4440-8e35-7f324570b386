import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import Screen from "@components/common/Screen";
import { ProfileNavParams } from "@navigation/profile-navigator/ProfileNavParams";
import {
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { Image, View } from "react-native";
import { z } from "zod";
import { trpc } from "@providers/RootProvider";
import { zodResolver } from "@hookform/resolvers/zod";
import AppFormInput from "@components/form/AppFormInput";
import AppFormDatePicker from "@components/form/AppFormDatePicker";
import AppFormSelect from "@components/form/AppFormSelect";
import { Option } from "@components/common/AppSelect";
import { GENDER, RACE } from "../../../../shared/types/user";
import { zString } from "@utils/schema";
import { useCallback, useEffect, useMemo } from "react";
import EmailPhoneUpdate from "app/elements/profile/EmailPhoneUpdate";
import { ENV, s3Paths } from "@constants/index";
import Toast from "react-native-toast-message";
import { useSession } from "@hooks/persistUser";
import * as ImagePicker from "expo-image-picker";
import { useUploadImage } from "@services/upload";
import { Image as CacheImage } from "react-native-expo-image-cache";
import errorHandler from "@utils/errorhandler";
import { imageKit } from "@utils/index";
import LogoSimple from "@assets/svg/LogoOutline";
import mixpanel from "@utils/mixpanel";
import Pressable from "@components/common/Pressable";
import _ from "lodash";
// import AppSelect from "@components/form/AppSelect";

const minAge = 12;
const minDate = new Date();
minDate.setFullYear(minDate.getFullYear() - minAge);

const profileSchema = z.object({
  race: z
    .enum([
      RACE.AMERICAN_INDIAN,
      RACE.ASIAN,
      RACE.BLACK,
      RACE.HAWAIIAN,
      RACE.PREFER_NO_SAY,
      RACE.WHITE,
    ])
    .or(z.string()),
  // dob: z.date().max(minDate, "You must be at least 12 years old"),
  gender: z
    .enum([
      GENDER.FEMALE,
      GENDER.MALE,
      GENDER.NON_BINARY,
      GENDER.PREFER_NOT_TO_SAY,
    ])
    .or(z.string()),
  city: zString,
  state: zString,
  zip: z
    .string()
    .min(5, "Zip code must be 5 characters")
    .max(5, "Zip code cannot contain more than 5 characters"),
  activity: z.string().optional(),
});

const updateUserPartialValidatorSchema = z.object({
  username: zString,
  firstname: zString,
  lastname: z.string(),
  year: z.string().optional(),
  month: z.string().optional(),
});

const schema = profileSchema.merge(updateUserPartialValidatorSchema);
type CareGiverProfileFormData = z.infer<
  typeof updateUserPartialValidatorSchema
>;

interface ProfileFormData {
  username: string;
  firstname: string;
  lastname: string;
  race: string;
  gender: string;
  city: string;
  state: string;
  zip: string;
  month?: string;
  year?: string;
  activity?: string;
}

export default function CreateProfile() {
  const navigation = useNavigation<StackNavigationProp<ProfileNavParams>>();
  const { userOrPatientId } =
    useRoute<RouteProp<ProfileNavParams, "CreateProfileScreen">>().params;

  const { user } = useSession();

  const isPatient = useMemo(
    () => userOrPatientId !== user?._id?.toString(),
    [user]
  );
  const userProfile = trpc.user.meOrPatientProfile.useQuery(userOrPatientId);

  const {
    data: me,
    status: meStatus,
    refetch: meRefetch,
  } = trpc.user.me.useQuery(userOrPatientId);

  const userProfileOptions = trpc.lib.userProfileOptions.useQuery();
  const states = trpc.lib.states.useQuery();
  const updateUserProfile =
    trpc.user.createUpdateUserOrPatientProfile.useMutation();
  const updateUserPartial = trpc.user.updateUserPartial.useMutation();
  const upsertUserDailyTracker =
    trpc.manage.upsertUserDailyTracker.useMutation();
  // const selectedTopicId = AsyncStorage.getItem("selectedTopic")
  const { data: topics } = trpc.manage.getTopics.useQuery({});
  const activityQuestion = _.find(
    _.get(_.head(topics), "dailyTrackerQuestions"),
    { isActivityQuestion: true }
  );

  const methods = useForm<ProfileFormData>({
    defaultValues: {
      activity: "",
    },
    resolver: zodResolver(schema),
    mode: "all",
  });

  useEffect(() => {
    if (userProfile?.status === "success" && meStatus === "success") {
      // @ts-ignore
      methods.reset({
        ...userProfile.data?.personal,
        ...userProfile.data?.address,
        ...me,
      });
    }
  }, [userProfile?.status, meStatus, userProfile.data]);

  console.log(methods.formState.errors);

  const utils = trpc.useUtils();

  const profileFormSubmit = methods.handleSubmit(async (data) => {
    const {
      city,
      dob,
      firstname,
      gender,
      lastname,
      race,
      state,
      username,
      zip,
      year,
      month,
      activity,
    } = data as ProfileFormData;

    // Save activity answers in user-daily-trackers
    if (activity && topics) {
      const selectedOption = activityQuestion?.options?.find(opt => opt.value === activity);
      if (selectedOption) {
        const activityAnswer = {
          question: activityQuestion?._id,
          selectedOption: [selectedOption._id],
          answerText: selectedOption.value,
          rawOptionData: {
            value: selectedOption.value,
            externalId: selectedOption.externalId,
            isDeleted: false,
            endOfQuestions: false
          }
        };

        // Create daily tracker entries for all topics
        for (const topic of topics) {
          await upsertUserDailyTracker.mutateAsync({
            topic: topic._id.toString(),
            date: new Date(),
            answers: [activityAnswer]
          });
        }
      }
    }

    await updateUserPartial.mutateAsync(
      {
        firstname,
        lastname,
        username,
        userOrPatientId,
        month,
        year,
        activity,
      },
      {
        onError(err) {
          alert(err.message);
          // @ts-ignore
          methods.setError("username", { message: err.message });
          if (isPatient) utils.user.getCareGiverPatients.invalidate();
          utils.user.me.invalidate();
          utils.user.meOrPatientProfile.invalidate(userOrPatientId);
        },
        onSuccess(data) {
          mixpanel.trackEvent(
            `Profile Updated (Profile Screen) `,
            {
              email: user?.email || "",
              phone: user?.contact?.phone || "",
              first_name: firstname || "",
              last_name: lastname || "",
              username: username || "",
              month: month || "",
              year: year || "",
              race: race || "",
              gender: gender || "",
              state: state || "",
              city,
              zip: zip || "",
              activity: activity || "",
            },
            String(user?._id),
            "v2"
          );
          meRefetch();
          userProfile.refetch();
          navigation.goBack();
        },
      }
    );
    
    await updateUserProfile.mutateAsync({
      address: { city, state, zip },
      personal: { dob, gender, race },
      userOrPatientId,
    });

  });
  const uploadImage = useUploadImage();

  const handleSelectImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.5,
    });

    if (!result.canceled) {
      handleUploadImage(result?.assets?.[0]?.uri);
    }
  };

  const handleProfileImageUpload = trpc.user.updateUserPartial.useMutation();

  const handleUploadImage = async (uri: string) => {
    try {
      const formdata = new FormData();
      if (uri) {
        formdata.append("filePath", s3Paths.profilePicture);
        formdata.append("image", {
          uri: uri,
          type: "image/png",
          name: "image.png",
        } as any);

        const {
          data: { s3Path },
        } = await uploadImage.mutateAsync({ formdata });

        await handleProfileImageUpload.mutateAsync(
          {
            profilePicture: s3Path,
            userOrPatientId,
          },
          {
            onSuccess: () => {
              utils.user.me.invalidate(userOrPatientId);
            },
          }
        );
      }

      // toast notification
      Toast.show({
        type: "success",
        text1: "profile picture added successfully",
        visibilityTime: 2000,
      });
    } catch (ex) {
      const err = errorHandler(ex);
      alert(err ? err : "Something went wrong");
    }
  };

  return (
    <Screen className="mx-4">
      <AppScrollView>
        <View className="flex-1 p-4">
          <FormProvider {...methods}>
            <View className="flex-1">
              <Pressable
                onPress={handleSelectImage}
                className="my-6"
                actionTag="create profile"
              >
                {me?.profilePicture ? (
                  <Image
                    source={{
                      uri: imageKit({
                        imagePath: me?.profilePicture,
                        transform: ["w-100"],
                      }),
                    }}
                    style={{
                      borderWidth: 0.2,
                      borderRadius: 50,
                      width: 100,
                      height: 100,
                    }}
                  />
                ) : (
                  <LogoSimple />
                )}
              </Pressable>

              <EmailPhoneUpdate userOrPatientId={userOrPatientId} />
              {/* user profile */}
              <View style={{ gap: 8 }}>
                <AppFormInput
                  name="firstname"
                  label="First Name"
                  placeholder="first name"
                />
                <AppFormInput
                  name="lastname"
                  label="Last Name"
                  placeholder="last name"
                />
                <AppFormInput
                  name="username"
                  label="Username"
                  placeholder="username"
                />
                {/* <AppFormDatePicker
                  name="dob"
                  label="Date of birth"
                  placeholder="d.o.b"
                /> */}
                <AppFormInput
                  name="month"
                  label="Month (MM)"
                  placeholder="Month (MM)"
                />
                <AppFormInput
                  name="year"
                  label="Year (YYYY)"
                  placeholder="Year (YYYY)"
                />
                <AppFormSelect name="race" label="Race" placeholder="race">
                  {(
                    Object.values(userProfileOptions.data?.race || {}) || []
                  ).map((race) => {
                    return <Option key={race} value={race} label={race} />;
                  })}
                </AppFormSelect>
                <AppFormSelect
                  name="gender"
                  label="Gender"
                  placeholder="gender"
                >
                  {(
                    Object.values(userProfileOptions.data?.gender || {}) || []
                  ).map((gender) => {
                    return (
                      <Option key={gender} value={gender} label={gender} />
                    );
                  })}
                </AppFormSelect>
                <AppFormSelect name="state" label="State" placeholder="state">
                  {/* @ts-ignore */}
                  {(Object.values(states.data || {}) || []).map((state) => {
                    // @ts-ignore
                    return <Option key={state} value={state} label={state} />;
                  })}
                </AppFormSelect>
                <AppFormInput name="city" label="City" placeholder="city" />
                <AppFormInput
                  name="zip"
                  label="Zip"
                  placeholder="zip"
                  maxLength={5}
                />
                <AppFormSelect
                  name="activity"
                  label={activityQuestion?.question || "I'm looking for"}
                  placeholder="Select activity"
                >
                  {(activityQuestion?.options || []).map((option) => (
                    <Option
                      key={option._id}
                      value={option.value}
                      label={option.value}
                    />
                  ))}
                </AppFormSelect>
              </View>
            </View>
          </FormProvider>
        </View>
      </AppScrollView>
      <View className="flex flex-row">
        <AppButton
          title="Continue"
          onPress={profileFormSubmit}
          isLoading={updateUserProfile.isLoading || updateUserPartial.isLoading}
        />
      </View>
    </Screen>
  );
}
