import GroupIcon from "@assets/svg/GroupIcon";
import HeartHealth from "@assets/svg/HeartHealth";
import LungHealth from "@assets/svg/LungHealth";
import RibbonIcon from "@assets/svg/RibbonIcon";
import AppText from "@components/common/AppText";
import Pressable from "@components/common/Pressable";
import StreakAnimation from "@components/common/Streak";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import { trpc } from "@providers/RootProvider";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { endOfDay, startOfWeek } from "date-fns";
import _ from "lodash";
import { useEffect, useMemo, useRef, useState } from "react";
import { Image, Platform, View } from "react-native";
import { RouterOutput } from "../../../../shared";
import { AnimatedNumber } from "../../components/AnimatedNumber";
import ManageSubscribedTopicListCard from "./ManageSubscribedTopicListCard";
import { PlusIcon } from "lucide-react-native";

const CATEGORY_ICONS = {
  Respiratory: <LungHealth color="#5351E8" />,
  Cancer: <RibbonIcon height={24} />,
  "Heart Health": <HeartHealth />,
};

export default function ManageHomeScreenPills({
  dailyTrackers = [],
  topics,
  selectedWeekDay,
  streak,
  userTopics,
  selectedTopic,
  onTopicSelect,
  fromConnectScreen = false,
}: {
  dailyTrackers?: any;
  streak: any;
  topics: RouterOutput["manage"]["getTopics"];
  selectedWeekDay?: Date;
  userTopics: RouterOutput["manage"]["getUserTopics"];
  selectedTopic?: string;
  onTopicSelect?: (topicId: string) => void;
  fromConnectScreen?: boolean;
}) {
  const [isVisible, setIsVisible] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ManageNavParams>>();
  const userSelectedTopics = useMemo(() => topics[0], [topics]);
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });
  const iconRef = useRef<View>(null);
  const [autoplay, setAutoplay] = useState(false);
  const prevStreakRef = useRef(streak?.currentStreak);

  const userDailyTrackersKey = {
    startDate: startOfWeek(new Date(), { weekStartsOn: 0 }),
    endDate: endOfDay(new Date()),
    topicId: userSelectedTopics?._id.toString(),
  };

  const handleIconLayout = (event: any) => {
    const { x, y, height } = event.nativeEvent.layout;
    setModalPosition({ x, y: y + height + 8 });
  };

  const {
    data: userDailyTrackers,
    isLoading: trackersLoading,
    refetch: refetchTrackers,
  } = trpc.manage.getUserDailyTrackers.useQuery(userDailyTrackersKey, {
    enabled: !!topics.length,
  });

  useEffect(() => {
    if (streak?.currentStreak) {
      refetchTrackers();
    }
  }, [streak?.currentStreak, refetchTrackers]);

  useEffect(() => {
    if (streak?.currentStreak !== prevStreakRef.current) {
      setAutoplay(true);

      const timeout = setTimeout(() => {
        setAutoplay(false);
      }, 2000); // 2 seconds

      prevStreakRef.current = streak?.currentStreak;

      // return () => clearTimeout(timeout);
    }
  }, [streak?.currentStreak]);

  const createdGroups = trpc.rabbleGroups.getUserCreatedGroups.useQuery();
  const joinedGroups = trpc.rabbleGroups.userJoinedGroups.useQuery();

  const groupsCount = useMemo(() => {
    const groups = _.uniqBy(
      [
        ...(createdGroups.data || []).map((_) => ({ ..._, owner: true })),
        ...(joinedGroups.data || []).map((_) => ({
          ..._.rabbleGroup,
        })),
      ],
      "_id"
    );

    return groups.length;
  }, [createdGroups.data, joinedGroups.data]);

  const selectedTopicData = useMemo(
    () =>
      _.find(
        userTopics,
        (userTopic) => userTopic?.topic?._id?.toString() === selectedTopic
      ),
    [userTopics, selectedTopic]
  );

  const TopicIcon = useMemo(
    () =>
      CATEGORY_ICONS[selectedTopicData?.topic?.category] ||
      CATEGORY_ICONS[userTopics[0]?.topic?.category] || (
        <PlusIcon color="#B4DDFF" />
      ),
    [selectedTopicData]
  );
  return (
    <View className="flex-row flex-wrap" style={{ gap: 8 }}>
      {/* Lung Health Icon */}
      <Pressable
        ref={iconRef}
        onLayout={handleIconLayout}
        onPress={() => {
          // navigation.navigate('TopicsListScreen');
          setIsVisible(true);
        }}
        className="flex items-center w-[78px] h-[30px] rounded-full bg-white shadow-lg shadow-primary-light/10"
        style={
          Platform.OS !== "ios"
            ? {
                shadowColor: "#000000",
                shadowOffset: { width: 10, height: 10 },
                shadowOpacity: 6,
                shadowRadius: 6,
                elevation: 4,
              }
            : {
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.2,
                shadowRadius: 6,
                elevation: 8,
              }
        }
      >
        <View className="flex flex-row relative" style={{ gap: 6 }}>
          <View className="mt-1 flex">
            {/* <LungHealth color="#5351E8" /> */}
            {selectedTopicData?.topic?.logo ? (
              <Image
                source={{ uri: selectedTopicData?.topic?.logo }}
                style={{ width: 24, height: 24 }}
              />
            ) : (
              TopicIcon
            )}
          </View>
          <View className="">
            <ManageSubscribedTopicListCard
              isVisible={isVisible}
              setIsVisible={setIsVisible}
              userTopics={userTopics}
              selectedTopic={selectedTopic}
              onTopicSelect={onTopicSelect}
              fromConnectScreen={fromConnectScreen}
            />
          </View>
        </View>
      </Pressable>

      {/* Streak Count with Flame Icon */}
      <Pressable
        onPress={() =>
          navigation.navigate("ManageStreakScreen", {
            streakCount: streak?.currentStreak,
            dailyTrackers: userDailyTrackers,
          })
        }
        className="flex items-center w-[78px] h-[30px] rounded-full bg-white shadow-md shadow-primary-light/10"
        style={
          Platform.OS !== "ios"
            ? {
                shadowColor: "#000000",
                shadowOffset: { width: 10, height: 10 },
                shadowOpacity: 6,
                shadowRadius: 6,
                elevation: 4,
              }
            : {
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.2,
                shadowRadius: 6,
                elevation: 8,
              }
        }
      >
        <View className="flex flex-row" style={{ gap: 6 }}>
          <View className="-mt-1">
            {/* <FlameIcon /> */}
            <StreakAnimation autoPlay={autoplay} height={30} width={30} />
          </View>
          <AnimatedNumber value={streak?.currentStreak} />
        </View>
      </Pressable>

      {/* Group Icon */}
      <Pressable
        onPress={() => {
          //@ts-ignore
          navigation.navigate("ConnectNavigator", { screen: "RabbleGroups" });
        }}
        className="flex items-center w-[78px] h-[30px] rounded-full bg-white shadow-lg shadow-primary-light/10"
        style={
          Platform.OS !== "ios"
            ? {
                shadowColor: "#000000",
                shadowOffset: { width: 10, height: 10 },
                shadowOpacity: 6,
                shadowRadius: 6,
                elevation: 4,
              }
            : {
                shadowColor: "#000000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.2,
                shadowRadius: 6,
                elevation: 8,
              }
        }
      >
        <View className="flex flex-row" style={{ gap: 6 }}>
          <View className="mt-1">
            <GroupIcon />
          </View>
          <AppText className="text-[#0B79D3] text-center font-montserrat text-[18px] font-semibold leading-normal mt-1.5">
            {groupsCount}
          </AppText>
        </View>
      </Pressable>
    </View>
  );
}
