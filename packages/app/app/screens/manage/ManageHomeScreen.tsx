import Confetti from "@components/common/Confetti";
import LoadingScreen from "@components/common/LoadingScreen";
import Screen from "@components/common/Screen";
import { ManageNewSubscription } from "@components/manage/ManageNewSubscription";
import { ManageNavParams } from "@navigation/manage-navigator/ManageNavParams";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { trpc } from "@providers/RootProvider";
import {
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import ManageDailyTrackersQuestionsSection from "app/elements/manage/ManageDailyTrackersQuestionsSection";
import ManageWeeklyInfoSection from "app/elements/manage/ManageWeeklyInfoSection";
import ManageProgressAndCalendarCard from "app/elements/manage/ManageProgressAndCalenderCard";
import clsx from "clsx";
import {
  endOfDay,
  endOfWeek,
  startOfDay,
  startOfToday,
  startOfWeek,
  subDays,
} from "date-fns";
import * as Location from "expo-location";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Platform, ScrollView, View } from "react-native";
import { BlogStatus } from "../../../../shared/types/Blog";
import ManageHomeScreenPills from "./ManageHomeScreenPills";
// import ButterFlyIcon from "@assets/svg/ButterFlyIcon";

import AsyncStorage from "@react-native-async-storage/async-storage";
import mixpanel from "@utils/mixpanel";
import * as React from "react";
import Svg, { Path, SvgProps } from "react-native-svg";
import { useSession } from "../../hooks/persistUser";
import InvitationRequestModal from "./InvitationRequests";
import ManageAirQualityIndex from "./ManageAirQualityIndex";

export function ButterFlyIcon(props: SvgProps) {
  return (
    <Svg
      width={41}
      height={41}
      viewBox="0 0 41 41"
      fill="none"
      // xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M17.061 40.26a3.784 3.784 0 01-.577-.04c-.77-.122-.94-.514-.963-.735-.08-.577.535-1.195 1.88-1.895-3.498 1.539-7.007 2.116-10.16 1.635-4.99-.77-6.518-4.615-6.986-6.852-.789-3.771.324-8.362 2.335-9.633.886-.557 3.734-1.296 6.002-1.511-.027-.035-.056-.07-.08-.104-.95-1.208-1.69-2.598-1.983-3.718-.896-3.39 3.16-14.508 3.333-14.979.031-.082.745-1.923 2.262-2.146 1.078-.16 2.225.535 3.41 2.07C16.94 4.17 27.34 17.928 28.24 27.78a17.554 17.554 0 001.565-2.731c3.386-7.462.937-14.939-2.483-19.445-1.019-1.346-1.871-1.98-2.464-1.844-1.238.283-1.975 3.92-2.514 6.575a.383.383 0 01-.454.3.385.385 0 01-.3-.454c.804-3.969 1.497-6.806 3.097-7.171.949-.217 1.98.46 3.247 2.129 3.555 4.685 6.098 12.458 2.572 20.227a19.277 19.277 0 01-2.216 3.64v.408c.38-.348.802-.646 1.257-.888 1.347-2.783 4.46-4.833 8.44-5.473 1.57-.254 2.335-.135 2.559.396.294.692-.726 1.538-1.178 1.884a.388.388 0 01-.54-.078.385.385 0 01.08-.539c.43-.321.846-.77.914-.961-.11-.047-.5-.143-1.713.053-3.465.556-6.212 2.226-7.577 4.506h.064c.54.081 1 .475 1.403 1.206.011.018.02.038.027.058.385 1.075.175 2.044-.537 2.59a1.583 1.583 0 01-1.848.1c-.32-.215-1.005-.892-.558-2.525-.271.22-.564.483-.876.77a8.395 8.395 0 01-.75 2.613C25.07 38.048 19.72 40.26 17.06 40.26zm-.77-.898c.099.065.495.192 1.411.08 2.15-.273 6.814-2.01 9.066-6.636.21-.435.373-.89.487-1.358-2.02 1.958-4.78 4.615-8.933 6.539-1.694.798-1.988 1.267-2.034 1.377l.003-.002zM9.22 21.956c-.626.039-1.25.11-1.869.215-1.876.3-3.709.813-4.356 1.221-1.566.99-2.779 5.062-1.992 8.825.428 2.037 1.82 5.558 6.352 6.248 4.079.623 8.806-.62 13.309-3.504a27.635 27.635 0 006.856-6.194c-.087-3.236-1.397-7.402-3.896-12.308-3.393-6.656-7.846-12.527-8.698-13.636-.988-1.28-1.916-1.895-2.695-1.781-1.059.157-1.648 1.64-1.654 1.654-.04.11-4.146 11.357-3.316 14.52.271 1.036.962 2.334 1.873 3.474.14.179.275.339.404.483.803-.017 1.457.061 1.811.28a.731.731 0 01.385.714c-.065.733-.527.85-.722.865-.552.049-1.23-.492-1.792-1.076zm20.9 7.169c-.11.258-.204.523-.28.792-.235.823-.147 1.467.236 1.731a.82.82 0 00.945-.073c.43-.33.537-.962.291-1.696-.273-.489-.545-.746-.828-.789a.842.842 0 00-.364.035zm-2.64.902c-.257.306-.524.608-.801.906l.098-.095c.24-.234.472-.457.691-.665l.012-.146zM10.37 21.965c.158.147.352.25.562.302a.506.506 0 00.034-.15 1.347 1.347 0 00-.597-.152z"
        fill="#FF8E1C"
      />
    </Svg>
  );
}

export default function ManageHomeScreen() {
  const { params } = useRoute<RouteProp<ManageNavParams, "ManageHomeScreen">>();
  const [topicPressed, setTopicPressed] = useState("");
  const [explodeConfetti, setExplodeConfetti] = useState(false);
  const [location, setLocation] = useState({});
  const [userSelectedTopic, setUserSelectedTopic] = useState<string>();
  const [isCarePartner, setIsCarePartner] = useState(false);
  const [showInvitationModal, setShowInvitationModal] = useState(false);
  const [filterTags, setFilterTags] = useState<string[]>([]);
  const { user } = useSession();
  const streak = trpc.manage.getStreak.useQuery(
    { topicId: userSelectedTopic || "" },
    {
      enabled: !!userSelectedTopic,
      refetchOnReconnect: false,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    }
  );

  const { data: rabbleInvites, refetch: refetchRabbleInvites } =
    trpc.rabbleGroups.getRabbleInvitesWithEmail.useQuery(
      {
        email: user?.email,
        phone: user?.contact?.countryCode
          ? [user?.contact?.countryCode, user?.contact?.phone].join("")
          : user?.contact?.phone,
      },
      { enable: !!user }
    );

  const [selectedWeekDay, setSelectedWeekDay] = useState(
    params?.startOfWeek
      ? startOfDay(new Date(params.startOfWeek))
      : startOfToday()
  );

  useEffect(() => {
    if (params?.topicId) {
      setUserSelectedTopic(params.topicId); // Set only the first time
    } else {
      AsyncStorage.getItem("selectedTopic").then((v) => {
        if (v) {
          setUserSelectedTopic(v);
        }
      });
    }
  }, [params]);

  useEffect(() => {
    if (
      rabbleInvites &&
      rabbleInvites?.some((r) => r.requestStatus === "pending")
    ) {
      setShowInvitationModal(true);
    }
  }, [rabbleInvites]);

  useEffect(() => {
    setSelectedWeekDay(
      params?.startOfWeek
        ? startOfDay(new Date(params.startOfWeek))
        : startOfToday()
    );
  }, [params?.startOfWeek]);

  // useEffect(() => {
  //   if (userSelectedTopic) {
  //     streak.refetch();
  //   }
  // }, [userSelectedTopic]);

  useEffect(() => {
    (async () => {
      await mixpanel.trackEvent(
        "Home screen view",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
        },
        String(user?._id),
        "v2"
      );
    })();
  }, []);

  const topics = trpc.manage.getTopics.useQuery(
    { topicIds: [userSelectedTopic] },
    {
      enabled: !!userSelectedTopic,
    }
  );

  console.log({ userSelectedTopic, topics: topics.data });

  const userTopics = trpc.manage.getUserTopics.useQuery(
    {},
    {
      enabled: !!userSelectedTopic,
      onSuccess(data) {
        if (!data.length) return;
        setUserSelectedTopic((prev) => prev || String(_.last(data)?.topic._id));
        setIsCarePartner(!!data[0].isCarePartner);
      },
    }
  );

  const baseOptions = { status: BlogStatus.PUBLISHED };
  const blogs = trpc.blog.getBlogs.useQuery({
    ...baseOptions,
    diseaseTags: [
      ..._.flatMap(topics.data ?? [], (t: any) => t.tags),
      ...filterTags,
    ],
  });

  const dailyTrackers = trpc.manage.getUserDailyTrackers.useQuery(
    {
      startDate: startOfWeek(
        subDays(
          startOfDay(
            params?.startOfWeek
              ? new Date(params?.startOfWeek)
              : selectedWeekDay
          ),
          7
        )
      ),
      endDate: endOfWeek(new Date()),
      topicId: userSelectedTopic,
    },
    { enabled: false }
  );

  const {
    data: locationIpData,
    isLoading: locationLoading,
    error: locationError,
  } = trpc.lib.locationFromIp.useQuery(
    {},
    {
      enabled: _.isEmpty(location),
    }
  );

  // refetch on screen mount
  useFocusEffect(
    useCallback(() => {
      userTopics.refetch();
      dailyTrackers.refetch();
    }, [selectedWeekDay, userSelectedTopic])
  );

  const unSubTopic = trpc.manage.unsubscribeUserTopic.useMutation();

  const onCompletedAction = useCallback(() => {
    dailyTrackers.refetch();
    // streak.refetch();
    showConfettiAnimation();
  }, []);

  const showConfettiAnimation = useCallback(() => {
    setExplodeConfetti(true);
    setTimeout(() => {
      setExplodeConfetti(false);
    }, 2000);
  }, []);

  const onSuccessAction = useCallback(() => {
    dailyTrackers.refetch();
    // streak.refetch();
  }, []);

  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();

  const addTopics = () => navigation.navigate("TopicsListScreen");

  // Check if AQI card should be shown for asthma topics
  const shouldShowAQI = useMemo(() => {
    const result = userTopics.data?.some((userTopic) => {
      const matches =
        userTopic.topic._id?.toString() === userSelectedTopic &&
        userTopic.topic.name?.toLowerCase().startsWith("asthma");
      console.log("AQI Check:", {
        topicId: userTopic.topic._id?.toString(),
        userSelectedTopic,
        topicName: userTopic.topic.name,
        matches,
      });
      return matches;
    });
    console.log("Should show AQI:", result);
    return result;
  }, [userTopics.data, userSelectedTopic]);

  useEffect(() => {
    blogs.refetch();
  }, [filterTags]);

  useEffect(() => {
    (async () => {
      // Fetch location
      let { coords } = await Location.getCurrentPositionAsync({});
      setLocation({ lat: coords.latitude, lng: coords.longitude });
    })();
  }, []);

  if (userTopics.isLoading) return <LoadingScreen />;
  if (!userTopics.isLoading && userTopics.data?.length === 0)
    return (
      <Screen className="bg-[#f5f7f9] p-4">
        <ManageNewSubscription />
      </Screen>
    );

  return (
    <Screen
      className={clsx(
        "flex flex-1 bg-[#f5f7f9]",
        Platform.OS === "android" && "pt-10"
      )}
      includeHeader
      headerClasses="mx-4"
    >
      {explodeConfetti && <Confetti />}
      <ScrollView>
        <View className="">
          <View className="px-4">
            {/* <TopicBottomSheet
              onUnsubscribeTopic={() => {
                unSubTopic.mutateAsync({ topic: userSelectedTopic });
                userTopics.refetch();
              }}
              selectedTopic={userSelectedTopic}
              hideBottomSheet={() => {
                setTopicPressed("");
              }}
              bottomSheetVisible={!!topicPressed}
              topicName={
                topics.data?.find(
                  (topic) => topic._id?.toString() === userSelectedTopic
                )?.name
              }
            /> */}

            {/* User topic badges */}
            <View className="mb-4 -mt-5 flex-row items-center justify-between">
              {/* <ManageHomeScreenUserTopicBadges
                userTopics={userTopics.data ?? []}
                selectedTopic={userSelectedTopic}
                onTopicSelect={(topicId) => {
                  // TODO: Fix this
                  // setTopicPressed("");
                  setUserSelectedTopic(topicId);
                }}
              /> */}
              <ManageHomeScreenPills
                streak={streak.data}
                topics={topics.data ?? []}
                selectedWeekDay={selectedWeekDay}
                dailyTrakcer={dailyTrackers.data ?? []}
                userTopics={userTopics.data ?? []}
                selectedTopic={userSelectedTopic}
                onTopicSelect={(topicId) => {
                  // TODO: Fix this
                  // setTopicPressed("");
                  AsyncStorage.setItem("selectedTopic", topicId);
                  setUserSelectedTopic(topicId);
                }}
              />

              <View className="mb-2">
                {/* <Pressable className={""} onPress={addTopics}> */}
                <ButterFlyIcon height={80} width={40} />
                {/* </Pressable> */}
              </View>
            </View>
          </View>

          {/* Weekly Info Section */}
          <View className="px-4 -mt-8">
            <ManageWeeklyInfoSection
              selectedWeekDay={selectedWeekDay}
              onDateSelect={setSelectedWeekDay}
              dailyTrackers={dailyTrackers.data ?? []}
              startDate={startOfDay(new Date())}
              endDate={endOfWeek(new Date())}
            />
          </View>

          {/* Daily tracker questions section */}

          <ManageDailyTrackersQuestionsSection
            selectedWeekDay={selectedWeekDay}
            isCarePartner={isCarePartner}
            topics={topics.data ?? []}
            onCompletedAction={onCompletedAction}
            onSuccessAction={onSuccessAction}
            onAnimationComplete={() => {}}
            streak={streak}
            lat={location?.lat || locationIpData?.latitude || 0}
            lng={location?.lng || locationIpData?.longitude || 0}
          />

          {/*Learnings*/}
          {/* <View className="mx-4 mt-8 mb-4">
            <AppText className="text-[20px] color-[#336D9F] font-montserratSemiBold">
              Learnings for you
            </AppText>

            <BlogList
              data={blogs.data}
              setFilterTags={setFilterTags}
              filterTags={filterTags}
              userSelectedTopic={userSelectedTopic}
            />
          </View> */}

          {/* Show AQI card only if selected topic name starts with "asthma" (case insensitive) */}
          {shouldShowAQI && (
            <View className="mx-4 mt-4">
              <ManageAirQualityIndex />
            </View>
          )}

          {/* TODO: Report Download Card - Always visible */}
          <View className="mx-4 mt-4">
            <ManageProgressAndCalendarCard selectedTopic={topics.data?.[0]} />
          </View>

          {/* <NotificationBelle /> */}
        </View>
      </ScrollView>
      <InvitationRequestModal
        visible={showInvitationModal}
        requests={rabbleInvites}
        onClose={() => {
          setShowInvitationModal(false);
          refetchRabbleInvites();
        }}
      />
    </Screen>
  );
}
