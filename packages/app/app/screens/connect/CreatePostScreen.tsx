import { ImgSelect } from "@assets/svg/connect/ImgSelect";
import AppText from "@components/common/AppText";
import AppButton from "@components/common/AppButton";
import AppScrollView from "@components/common/AppScrollView";
import usePersistedUser, { useSession } from "@hooks/persistUser";
import { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
  View,
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import errorHandler from "@utils/errorhandler";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import Toast from "react-native-toast-message";
import LogoSimple from "@assets/svg/LogoOutline";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import { trpc } from "@providers/RootProvider";
import { useUploadImage } from "@services/upload";
import { s3Paths } from "@constants/index";
import _ from "lodash";
import mixpanel from "@utils/mixpanel";
import Pressable from "@components/common/Pressable";
import AppTextInput from "@components/common/AppTextInput";
import Rocket from "@assets/svg/connect/Rocket";
import { Camera, Speak } from "@screens/rabbles/PostOrGroupPostDetailsScreen";
import Screen from "@components/common/Screen";
import { ChevronLeft } from "lucide-react-native";

const { width } = Dimensions.get("screen");
const MARGIN = 16;
const postImageWidth = width - 2 * MARGIN;
const postImageHeight = (postImageWidth / 4) * 3;

export default function CreatePostScreen() {
  const navigation = useNavigation();
  const [keyboardOffset, setKeyboardOffset] = useState(0);

  const handleKeyboardShow = (event: any) => {
    const keyboardHeight = event.endCoordinates.height;
    setKeyboardOffset(keyboardHeight);
  };

  const handleKeyboardHide = () => {
    setKeyboardOffset(0);
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      handleKeyboardShow
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      handleKeyboardHide
    );
    // Clean up listeners
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const { user, isLoading } = useSession();

  const [selectedImage, setSelectedImage] = useState<
    ImagePicker.ImagePickerAsset | undefined
  >(undefined);
  const [caption, setCaption] = useState("");

  const handleSelectImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.5,
    });

    if (!result.canceled) {
      setSelectedImage(result.assets[0]);
    }
  };

  const { postType, rabbleGroup, connectPost } =
    useRoute<RouteProp<RootNavParams, "CreatePostScreen">>().params;
  console.log({ connectPost });
  const createPost = trpc.connect.createPost.useMutation();
  const editPost = trpc.connect.editPost.useMutation();
  const groupInfo = trpc.rabbleGroups.getGroupInfo.useQuery({
    group: rabbleGroup,
  });
  const { data: groupUser } = trpc.user.me.useQuery(
    String(groupInfo?.data?.createdBy),
    { enable: !!groupInfo?.data }
  );
  const uploadImage = useUploadImage();

  const utils = trpc.useUtils();

  const handleEditPost = async () => {
    try {
      // Disable the button if already loading or no image/caption selected
      if (
        editPost.isLoading ||
        uploadImage.isLoading ||
        !(selectedImage || caption)
      )
        return;

      const formdata = new FormData();
      if (selectedImage?.uri) {
        formdata.append("filePath", s3Paths.post);
        formdata.append("image", {
          uri: selectedImage?.uri,
          type: "image/png",
          name: "image.png",
        } as any);

        const {
          data: { s3Path },
        } = await uploadImage.mutateAsync({ formdata });

        await editPost.mutateAsync({
          postId: connectPost?._id,
          postType: Array.isArray(postType) ? postType[0] : postType,
          rabbleGroup,
          title: caption,
          image: s3Path,
        });
      } else {
        await editPost.mutateAsync({
          postId: connectPost?._id,
          postType: Array.isArray(postType) ? postType[0] : postType,
          rabbleGroup,
          title: caption,
        });
      }

      setCaption("");
      setSelectedImage(undefined);

      // Show success toast notification
      Toast.show({
        type: "success",
        text1: "post updated successfully",
        visibilityTime: 2000,
      });

      // Invalidate
      // utils.connect.getPosts.invalidate();
      navigation.goBack();
    } catch (ex) {
      // Handle errors
      const err = errorHandler(ex);
      alert(err ? err : "Something went wrong");
    }
  };

  const handleCreatePost = async () => {
    try {
      // Disable the button if already loading or no image/caption selected
      let imageShared = "";
      if (
        createPost.isLoading ||
        uploadImage.isLoading ||
        !(selectedImage || caption)
      )
        return;

      const formdata = new FormData();
      if (selectedImage?.uri) {
        formdata.append("filePath", s3Paths.post);
        formdata.append("image", {
          uri: selectedImage?.uri,
          type: "image/png",
          name: "image.png",
        } as any);

        const {
          data: { s3Path },
        } = await uploadImage.mutateAsync({ formdata });
        imageShared = s3Path;
        const createdPost = await createPost.mutateAsync({
          postType: Array.isArray(postType) ? postType[0] : postType,
          rabbleGroup,
          title: caption,
          image: s3Path,
        });
        await mixpanel.trackEvent(
          "Post created (Rabble screen)",
          {
            email: user?.email || "",
            phone: user?.contact?.phone || "",
            post_content: caption,
            post_id: createdPost?._id || "", // need confirm
            images_shared: imageShared || "",
            time: new Date().toISOString(),
            group_title: groupInfo?.data?.groupName || "",
            group_status: "Inactive",
            group_admin:
              `${groupUser?.firstname || ""} ${groupUser?.lastname || ""}` ||
              "",
            group_id: rabbleGroup || "",
          },
          String(user?._id),
          "v2"
        );
      } else {
        const createdPost = await createPost.mutateAsync({
          postType: Array.isArray(postType) ? postType[0] : postType,
          rabbleGroup,
          title: caption,
        });
        await mixpanel.trackEvent(
          "Post created (Rabble screen)",
          {
            email: user?.email || "",
            phone: user?.contact?.phone || "",
            post_content: caption,
            post_id: createdPost?._id || "", // need confirm
            images_shared: imageShared || "",
            time: new Date().toISOString(),
            group_title: groupInfo?.data?.groupName || "",
            group_status: !rabbleGroup ? "Inactive" : `Active`,
            group_admin:
              `${groupUser?.firstname || ""} ${groupUser?.lastname || ""}` ||
              "",
            group_id: rabbleGroup || "",
          },
          String(user?._id),
          "v2"
        );
      }

      setCaption("");
      setSelectedImage(undefined);

      // Show success toast notification
      Toast.show({
        type: "success",
        text1: "post created successfully",
        visibilityTime: 2000,
      });

      // Invalidate
      // utils.connect.getPosts.invalidate();
      navigation.goBack();
    } catch (ex) {
      // Handle errors
      const err = errorHandler(ex);
      alert(err ? err : "Something went wrong");
    }
  };

  const captionRef = useRef<TextInput>(null);

  const handleCaptionInputFocus = () => {
    if (captionRef.current) captionRef.current.focus();
  };

  useEffect(() => {
    if (_.size(connectPost)) {
      setCaption(connectPost?.title);
      // setSelectedImage(connectPost?.image);
      // handleSelectImage()
      navigation.setOptions({ headerTitle: "update post" });
    }
  }, [connectPost]);

  if (isLoading) return null;

  return (
    // <View className="flex flex-col flex-1 bg-white mb-6  px-4">
    //   <>
    //     {/* Post header */}
    //     <View className="flex flex-row items-center justify-between mt-4">
    //       {/* DP and Name */}
    //       <View className="flex flex-row flex-1 items-center">
    //         {user?.profilePicture ? (
    //           <Image
    //             className="w-10 h-10 rounded-full mr-2"
    //             source={{ uri: user?.profilePicture }}
    //           />
    //         ) : (
    //           <LogoSimple width={32} height={32} className="mr-2" />
    //         )}
    //         <AppText className="font-montserratMedium flex-1" numberOfLines={1}>
    //           {user?.username || user?.firstname}
    //         </AppText>
    //       </View>

    //       {/* Post button */}
    //       <View className="h-10">
    //         <AppButton
    //           title={_.size(connectPost) ? "Update Post" : "Post"}
    //           variant={!!(selectedImage || caption) ? "primary" : "disabled"}
    //           disabled={
    //             !(
    //               selectedImage ||
    //               caption ||
    //               uploadImage.isLoading ||
    //               createPost.isLoading
    //             )
    //           }
    //           onPress={_.size(connectPost) ? handleEditPost : handleCreatePost}
    //           isLoading={createPost.isLoading}
    //           size="small"
    //           className="min-w-[70px]"
    //         />
    //       </View>
    //     </View>
    //     {/* Text */}
    //     <AppText onPress={handleCaptionInputFocus} className="mt-4">
    //       {caption || `What's on your mind?`}
    //     </AppText>
    //   </>
    //   <View className="flex flex-1">
    //     {selectedImage && (
    //       <Image
    //         source={{ uri: selectedImage.uri }}
    //         resizeMode="cover"
    //         className="mt-4 rounded-lg"
    //         style={{ width: postImageWidth, height: postImageHeight }}
    //       />
    //     )}
    //   </View>
    //   <View
    //     style={
    //       keyboardOffset
    //         ? {
    //             alignSelf: "flex-end",
    //             bottom: Platform.OS === "android" ? 0 : keyboardOffset,
    //           }
    //         : {}
    //     }
    //     className="flex flex-row items-center justify-between border-t border-neutral-300 bg-white"
    //   >
    //     <TextInput
    //       onBlur={() => Keyboard.dismiss()}
    //       enterKeyHint="done"
    //       blurOnSubmit={true}
    //       ref={captionRef}
    //       className="p-3 font-montserratRegular flex-1"
    //       placeholder="Share your thoughts..."
    //       placeholderTextColor="#64748B"
    //       multiline
    //       onChangeText={setCaption}
    //       value={caption}
    //     />
    //     <ImgSelect className="" onPress={handleSelectImage} />
    //   </View>
    // </View>
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1, ...(Platform.OS !== "ios" && { marginTop: 32 }) }}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <Screen className="flex-1 bg-[#F5F7F9]">
        <View className="flex-1">
          <View className="flex-1">
            <View className="flex-row items-center mt-4 px-4">
              <ChevronLeft
                color="#0B79D3"
                size={22}
                onPress={() => navigation.goBack()}
                className="mr-4"
              />
              <Text className="text-[#0B79D3]">Create Post</Text>
            </View>
          </View>
          <View className="flex flex-1">
            {selectedImage && (
              <Image
                source={{ uri: selectedImage.uri }}
                resizeMode="cover"
                className="mt-4 rounded-lg"
                style={{ width: postImageWidth, height: postImageHeight }}
              />
            )}
          </View>
          <View className="flex-row items-center justify-between px-4 py-3">
            {/* <Plus /> */}
            <Pressable className="flex-1 px-2" onPress={handleCreatePost}>
              <AppTextInput
                // placeholder="Share something here..."
                borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF] bg-white"
                placeholderTextColor="rgba(0, 73, 135, 0.40)"
                className="h-14 text-[18px] w-full"
                wrapperClass={`${caption ? "mr-0" : "mr-2"}`}
                multiline
                onChangeText={setCaption}
                value={caption}
                //   onPress={handleCreatePost}
              />
            </Pressable>
            {caption && (
              <Pressable
                actionTag="create post"
                onPress={handleCreatePost}
                className="bg-primary w-[50px] h-[50px] justify-center items-center rounded-lg ml-1"
                //   disabled={createPost?.status === "loading"}
                style={{ backgroundColor: caption ? "#004987" : "#ccc" }}
              >
                {createPost?.isLoading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Rocket />
                )}
              </Pressable>
            )}
            {!caption && (
              <Camera onPress={() => Alert.alert("Coming Soon")} className="mr-2" />
            )}
            {!caption && (
              <Speak
                onPress={() => Alert.alert("Coming Soon")}
                className="ml-2"
              />
            )}
          </View>
        </View>
      </Screen>
    </KeyboardAvoidingView>
  );
}

CreatePostScreen.navigationOptions = {
  tabBarVisible: false,
};
