import { imageKit } from "@utils/index";
import { ChevronLeft } from "lucide-react-native";
import { Al<PERSON>, KeyboardAvoidingView, Text } from "react-native";
import { Image } from "react-native-expo-image-cache";
import Svg, { Path, SvgProps } from "react-native-svg";
import ChatMessage from "./ChatMessge";
import { useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import Screen from "@components/common/Screen";
import { ScrollView } from "react-native-gesture-handler";
import Rocket from "@assets/svg/connect/Rocket";
import AppText from "@components/common/AppText";
import AppScrollView from "@components/common/AppScrollView";
import ConnectPost from "@components/connect/Post";
import { RouteProp, useRoute } from "@react-navigation/native";
import { useCallback, useRef, useState } from "react";
import { ActivityIndicator, Platform, View, Clipboard } from "react-native";
import useKeyboard from "@hooks/useKeyboard";
import { trpc } from "@providers/RootProvider";
import { useSession } from "@hooks/persistUser";
import { PostComment } from "@components/connect/PostComment";
import InfoModal from "@components/common/InfoModal";
import ModalBottomSheet from "@components/common/ModalBottomSheet";
import SectionLink from "@components/profile/ProfileSectionItem";
import Trash from "@assets/svg/connect/Trash";
import Report from "@assets/svg/connect/Report";
import Toast from "react-native-toast-message";
import errorHandler from "@utils/errorhandler";
import { Comment } from "../../../../shared/types/Connect";
import { RouterOutput } from "../../../../shared";
import Pressable from "@components/common/Pressable";
import mixpanel from "@utils/mixpanel";
import { Share } from "react-native";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import AppTextInput from "@components/common/AppTextInput";
import _ from "lodash";
import { format } from "date-fns";

export function DefaultIcon(props: SvgProps) {
  return (
    <Svg
      width="40px"
      height="41px"
      viewBox="0 0 66 66"
      fill="none"
      preserveAspectRatio="xMidYMid meet"
      {...props}
    >
      <Path
        d="M0 10C0 4.477 4.477 0 10 0h46c5.523 0 10 4.477 10 10v46c0 5.523-4.477 10-10 10H10C4.477 66 0 61.523 0 56V10z"
        fill="#004987"
      />
      <Path
        d="M57.543 16.752l-.98 10.09c-.543 5.57-5.983 9.697-12.147 9.206l-11.169-.885.98-10.09c.543-5.569 5.983-9.697 12.147-9.205l11.169.884z"
        fill="#fff"
      />
      <Path
        d="M48.099 46.105c-.38 3.93-4.225 6.846-8.576 6.502-4.352-.344-7.579-3.816-7.198-7.747l.689-7.126 7.887.623c4.351.344 7.579 3.816 7.198 7.748zM10.023 13l-.98 10.09c-.543 5.57 4.026 10.483 10.19 10.975l11.17.884.978-10.09c.544-5.57-4.025-10.483-10.19-10.975L10.024 13z"
        fill="#FF8E1C"
      />
      <Path
        d="M13.684 43.384c-.381 3.931 2.846 7.404 7.198 7.748 4.351.344 8.195-2.572 8.575-6.503l.69-7.125-7.887-.623c-4.352-.344-8.178 2.572-8.576 6.503z"
        fill="#fff"
      />
    </Svg>
  );
}

export const Camera = (props: SvgProps) => (
  <Svg width={22} height={19} viewBox="0 0 22 19" fill="none" {...props}>
    <Path
      d="M19.9071 3.44255H14.8984L13.352 0.865234H7.90559L6.3592 3.44255H1.35043C0.639087 3.44333 0.0625408 4.01987 0.0617676 4.73121V17.1023C0.0625408 17.8137 0.639087 18.3902 1.35043 18.391H19.9071C20.6185 18.3902 21.195 17.8137 21.1958 17.1023V4.73121C21.195 4.01987 20.6185 3.44333 19.9071 3.44255ZM8.19733 1.3807H13.0602L14.2973 3.44255H6.96022L8.19733 1.3807ZM19.9071 3.95802C20.3342 3.95802 20.6803 4.30415 20.6803 4.73121V17.1023C20.6803 17.5294 20.3342 17.8755 19.9071 17.8755H1.35043C0.923365 17.8755 0.577231 17.5294 0.577231 17.1023V4.73121C0.577231 4.30415 0.923365 3.95802 1.35043 3.95802H19.9071Z"
      fill="#0B79D3"
    />
    <Path
      d="M2.12362 7.824H5.47414V5.50441H2.12362V7.824ZM2.63909 6.01987H4.95867V7.30853H2.63909V6.01987Z"
      fill="#0B79D3"
    />
    <Path
      d="M10.6288 6.79307C8.06666 6.79307 5.9896 8.87013 5.9896 11.4322C5.9896 13.9944 8.06666 16.0714 10.6288 16.0714C13.1909 16.0714 15.268 13.9944 15.268 11.4322C15.2649 8.87142 13.1896 6.79616 10.6288 6.79307ZM10.6288 15.556C8.3512 15.556 6.50507 13.7098 6.50507 11.4322C6.50507 9.15467 8.3512 7.30853 10.6288 7.30853C12.9064 7.30853 14.7525 9.15467 14.7525 11.4322C14.7499 13.7085 12.9051 15.5534 10.6288 15.556Z"
      fill="#0B79D3"
    />
    <Path
      d="M10.6288 8.33946C8.92079 8.33946 7.53599 9.72425 7.53599 11.4322C7.53599 13.1402 8.92079 14.525 10.6288 14.525C12.3368 14.525 13.7216 13.1402 13.7216 11.4322C13.7198 9.72503 12.336 8.34127 10.6288 8.33946ZM10.6288 14.0096C9.20532 14.0096 8.05146 12.8557 8.05146 11.4322C8.05146 10.0088 9.20532 8.85493 10.6288 8.85493C12.0522 8.85493 13.2061 10.0088 13.2061 11.4322C13.2046 12.8549 12.0515 14.008 10.6288 14.0096Z"
      fill="#0B79D3"
    />
  </Svg>
);

export const Speak = (props: SvgProps) => (
  <Svg width={13} height={23} viewBox="0 0 13 23" fill="none" {...props}>
    <Path
      d="M6.5 15.668C8.36875 15.6659 9.88333 14.1513 9.88542 12.2826V3.42839C9.88542 1.55859 8.36979 0.0429688 6.5 0.0429688C4.63021 0.0429688 3.11458 1.55859 3.11458 3.42839V12.2826C3.11667 14.1513 4.63125 15.6659 6.5 15.668ZM3.63542 3.42839C3.63542 1.84635 4.91797 0.563802 6.5 0.563802C8.08203 0.563802 9.36458 1.84635 9.36458 3.42839V3.6888H7.28125V4.20964H9.36458V6.29297H7.28125V6.8138H9.36458V8.89713H7.28125V9.41797H9.36458V11.5013H7.28125V12.0221H9.36458V12.2826C9.36458 13.8646 8.08203 15.1471 6.5 15.1471C4.91797 15.1471 3.63542 13.8646 3.63542 12.2826V3.42839Z"
      fill="#0B79D3"
    />
    <Path
      d="M6.76042 21.918V17.738C9.67292 17.5982 11.9641 15.1984 11.9688 12.2826V9.67839H11.4479V12.2826C11.4479 15.0151 9.23255 17.2305 6.5 17.2305C3.76745 17.2305 1.55208 15.0151 1.55208 12.2826V9.67839H1.03125V12.2826C1.0362 15.1984 3.32708 17.5982 6.23958 17.738V21.918H0.25V22.4388H12.75V21.918H6.76042Z"
      fill="#0B79D3"
    />
  </Svg>
);

export const Plus = (props: SvgProps) => (
  <Svg width={22} height={22} viewBox="0 0 22 22" fill="none" {...props}>
    <Path
      d="M11.7181 1V21M21 11.7181H1"
      stroke="#0B79D3"
      strokeWidth={1.67}
      strokeLinecap="round"
    />
  </Svg>
);

interface ChatMessageProps {
  avatar?: string;
  name?: string;
  message?: string;
  time?: string;
  isOwn?: boolean;
  reactions?: { emoji: string; count: number }[];
}

const PostOrGroupPostDetailsScreen = ({}) => {
  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const {
    params: { postId, groupId, groupRole },
  } = useRoute<RouteProp<RootNavParams, "PostOrGroupPostDetailsScreen">>();
  const { user } = useSession();
  const {
    data: comments,
    isLoading: isCommentsLoading,
    refetch: refetchComments,
  } = trpc.connect.getComments.useQuery({ post: postId });
  const groupInfo = trpc.rabbleGroups.getGroupInfo.useQuery(
    {
      group: groupId || "",
    },
    { enabled: !!groupId }
  );

  const {
    data: post,
    isLoading: isPostsLoading,
    refetch: refetchPost,
  } = trpc.connect.getPosts.useInfiniteQuery(
    { limit: 30, postId, user: user?._id as string },
    {
      initialCursor: 0,
    }
  );

  const { status: createCommentStatus, mutateAsync: createCommentAsync } =
    trpc.connect.createComment.useMutation();
  const getUserById = trpc.user.getUserById.useMutation();

  // State
  const [comment, setComment] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedComment, setSelectedComment] =
    useState<RouterOutput["connect"]["getComments"][number]>();
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [replyToComment, setReplyToComment] = useState<Comment | null>(null);
  const inputRef = useRef<any>(null);

  const handleCommentOptions = useCallback((comment: Comment) => {
    setSelectedComment(comment);
    setIsModalOpen(true);
    setDeleteModalVisible(false);
    setReportModalVisible(false);
  }, []);

  const handleContextMenuClick = (
    _comment: Comment,
    mode?: "reply" | "report" | "delete"
  ) => {
    if (mode === "reply") {
      mixpanel.trackEvent(
        "Comment replied",
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
          // reply_content: comment || "",
          comment_id: _comment?._id?.toString() || "",
          post_id: postId || "",
          post_content: _comment?.comment,
        },
        String(user?._id),
        "v2"
      );
      setReplyToComment(_comment);
      setTimeout(() => inputRef.current?.focus(), 100);
    }

    if (mode === "report") {
      setReportModalVisible(true);
    }

    if (mode === "delete") {
      setDeleteModalVisible(true);
    }
  };

  const deleteAction = () => {
    // setIsModalOpen(false);
    setDeleteModalVisible(true);
  };

  const reportAction = () => {
    setIsModalOpen(false);
    setReportModalVisible(true);
  };

  const handleCreateComment = async () => {
    createCommentAsync(
      {
        post: postId,
        comment,
        ...(replyToComment?._id
          ? { repliedTo: replyToComment?._id?.toString() }
          : {}),
      },
      {
        onSuccess() {
          setComment("");
          setReplyToComment(null);
          refetchComments();
        },
      }
    );
    let groupUser = null;
    if (groupInfo?.data?.createdBy) {
      groupUser = await getUserById.mutateAsync(
        String(groupInfo?.data?.createdBy)
      );
    }
    // const groupUser = groupInfo?.data?.createdBy && await getUserById.mutateAsync(String(groupInfo?.data?.createdBy) || "")
    console.log({ postData: post?.pages?.at(0)?.posts?.at(0) });
    await mixpanel.trackEvent(
      "Post comment posed (Rabble screen)",
      {
        email: user?.email || "",
        phone: user?.contact?.phone || "",
        post_content: comment || "",
        post_username:
          post?.pages?.at(0)?.posts?.at(0)?.user[0]?.username || "",
        post_id: postId || "",
        time: new Date().toISOString(),
        group_title: groupInfo?.data?.groupName || "",
        group_status: !groupInfo?.data?.groupName ? "Inactive" : `Active`,
        group_admin:
          `${groupUser?.firstname || ""} ${groupUser?.lastname || ""}` || "",
        group_id: groupId || "",
      },
      String(user?._id),
      "v2"
    );
    console.log("commented");
  };

  const { mutateAsync: likePost, isLoading: isLikeLoading } =
    trpc.connect.likePost.useMutation();

  const _deleteComment = trpc.connect.deleteComment.useMutation();
  const _reportComment = trpc.connect.reportComment.useMutation();

  const likeComment = trpc.connect.likeComment.useMutation();

  const handleDeleteComment = async () => {
    try {
      await _deleteComment.mutate(selectedComment?._id!, {
        onSettled: () => {
          refetchComments();
          setDeleteModalVisible(false);
          setIsModalOpen(false);
        },
      });

      // handle toast
      Toast.show({
        type: "success",
        text1: "comment deleted successfully",
        visibilityTime: 2000,
      });
    } catch (ex) {
      errorHandler(ex);
    }
  };

  const handleReportComment = async () => {
    await _reportComment.mutate({ comment: selectedComment?._id! });
    // handle toast
    Toast.show({
      type: "success",
      text1: "comment reported successfully",
      visibilityTime: 2000,
    });
    setReportModalVisible(false);
  };

  const keyboard = useKeyboard();

  if (isCommentsLoading || isPostsLoading)
    return (
      <View className="flex flex-col flex-1 justify-center items-center">
        <View className="flex flex-row items-center">
          <ActivityIndicator color="#FF8E1C" className="mr-2" />
          <AppText className="font-montserratSemiBold text-[#ff8e1c]">
            loading post
          </AppText>
        </View>
      </View>
    );

  if (!post) return null;
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "padding"}
      style={{ flex: 1, ...(Platform.OS !== "ios" && { marginTop: 24 }) }}
      keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
    >
      <Screen className="flex-1 bg-[#F5F7F9]">
        <View className="flex-1">
          <View className="flex-1">
            {/* Header */}
            <View className="flex-row items-center mt-8 px-4">
              <ChevronLeft
                color="#0B79D3"
                size={22}
                onPress={() => navigation.goBack()}
              />
              {user?.imageUrl ? (
                <Image
                  style={{ width: 40, height: 41, borderRadius: 10 }}
                  uri={imageKit({
                    imagePath: user?.imageUrl,
                    transform: ["w-500"],
                  })}
                />
              ) : (
                <View className="ml-4">
                  <DefaultIcon />
                </View>
              )}
              <Pressable
                onPress={() => {
                  if(groupInfo?.data?.groupName) {
                  navigation.navigate("RabbleDetailsScreen", {
                    groupId,
                    isCareGiverManagedGroup: false,
                  });
                }
                }}
                className="ml-3 max-w-[65%]"
              >
                <Text numberOfLines={1} className="text-[#0B79D3] font-[Montserrat] text-[14px] font-[600]">
                  {groupInfo?.data?.groupName ||
                    `${
                      post?.pages?.at(0)?.posts?.at(0)?.user?.[0]?.firstname
                    } ${
                      post?.pages?.at(0)?.posts?.at(0)?.user?.[0]?.lastname
                    }` ||
                    "Fresh Air Forum"}
                </Text>
                <Text className="text-[#336D9F] font-[Montserrat] text-[10px] font-[500]">
                  Tap here for more info...
                </Text>
              </Pressable>
            </View>

            {/* Chat messages */}
            <ScrollView
              contentContainerStyle={{ padding: 16, paddingHorizontal: Platform.OS === "ios" ? 16 : 0, rowGap: 24 }}
              showsVerticalScrollIndicator={false}
            >
              {/* {[1, 2, 3].map((_, idx) => (
                <ChatMessage
                  key={idx}
                  name={"Carla Baker"}
                  message={
                    "Hey there! I’m attending a severe asthma webinar Friday, want to join me?"
                  }
                  time={"2:36 PM"}
                  reactions={[{ emoji: "❤️", count: 3 }]}
                />
              ))} */}
              <ConnectPost
                {...post?.pages?.at(0)?.posts?.at(0)}
                isLikeLoading={isLikeLoading}
                onLike={() =>
                  likePost(
                    {
                      post: postId,
                      status: !post?.pages.at(0)?.posts.at(0)?.isLiked,
                    },
                    {
                      onSettled() {
                        refetchPost();
                      },
                    }
                  )
                }
                showInComments
              />
              {_.map(comments, (comment, idx) => {
                const { user, createdAt, reactionTypes } = comment;
                console.log({ reactionTypes: _.last(reactionTypes) });
                return (
                  <PostComment
                    key={comment._id}
                    comments={comments}
                    commentOptionsCallback={handleCommentOptions}
                    onlikeComment={async (post, status, emogi?: any) => {
                      await likeComment.mutateAsync(
                        {
                          comment: comment._id as string,
                          status: emogi && status ? status : !status,
                          reactionType: emogi || "",
                        },
                        {
                          onSuccess: () => {
                            refetchComments();
                          },
                        }
                      );
                    }}
                    contextMenus={[
                      {
                        label: "Reply",
                        icon: "➡️",
                        onClick: () => handleContextMenuClick(comment, "reply"),
                      },
                      {
                        label: "Forward",
                        icon: "⏩",
                        onClick: () => {
                          mixpanel.trackEvent(
                            "Comment forwarded",
                            {
                              email: user?.email || "",
                              phone: user?.contact?.phone || "",
                              // message: comment.comment || "",
                              comment_id: comment?._id?.toString() || "",
                              post_id: postId || "",
                              forwarded_comment_content: comment.comment,
                            },
                            String(user?._id),
                            "v2"
                          );
                          Share.share({
                            message: comment.comment ?? "Check this out!",
                          });
                        },
                      },
                      {
                        label: "Copy",
                        icon: "📃",
                        onClick: () => {
                          mixpanel.trackEvent(
                            "Comment Copied",
                            {
                              email: user?.email || "",
                              phone: user?.contact?.phone || "",
                              // message: comment.comment || "",
                              comment_id: comment?._id?.toString() || "",
                              post_id: postId || "",
                              copy_comment_content: comment?.comment || "",
                            },
                            String(user?._id),
                            "v2"
                          );
                          Clipboard.setString(comment?.comment);
                        },
                      },
                      { label: "Star", icon: "⭐️" },
                      ...(String(user?._id) === String(comment?.user?._id)
                        ? [
                            {
                              label: "Delete",
                              icon: "🗑",
                              color: "#E81448",
                              onClick: () => {
                                mixpanel.trackEvent(
                                  "Comment deleted",
                                  {
                                    email: user?.email || "",
                                    phone: user?.contact?.phone || "",
                                    // message: comment.comment || "",
                                    comment_id: comment?._id?.toString() || "",
                                    post_id: postId || "",
                                    comment_content: comment?.comment || "",
                                  },
                                  String(user?._id),
                                  "v2"
                                );
                                setTimeout(() => {
                                  deleteAction();
                                }, 1000);
                              },
                            },
                          ]
                        : []),
                      { label: "More...", icon: "", color: "#0B79D3" },
                    ]}
                    {...comment}
                  />
                  // <ChatMessage
                  //   key={_comment._id}
                  //   name={user?.username || user?.firstname || ""}
                  //   message={comment}
                  //   time={format(new Date(createdAt), "MMM dd yyyy, hh:mm a")}
                  //   reactions={[{ emoji: _.last(reactionTypes) }]}
                  // />
                );
              })}
            </ScrollView>
          </View>

          {replyToComment && (
            <View className="bg-gray-100 p-2 px-16 rounded-t-lg border-t border-gray-300 mb-[-2]">
              <View className="flex-row justify-between items-center">
                <AppText className="text-sm font-bold">
                  Replying to {replyToComment.user.username}
                </AppText>
                <Pressable onPress={() => setReplyToComment(null)}>
                  <AppText className="text-xs text-red-500">Cancel</AppText>
                </Pressable>
              </View>
              <AppText className="text-xs text-gray-500 mt-1">
                {replyToComment.comment}
              </AppText>
            </View>
          )}

          {/* Bottom Input Bar */}
          <View className="flex-row items-center justify-between px-4 py-3">
            {/* <Plus /> */}
            <View className="flex-1 px-2">
              <AppTextInput
                // placeholder="Share your thoughts..."
                borderClass="border border-[#B4DDFF] border-b-[5px] border-b-[#B4DDFF] bg-white"
                placeholderTextColor="rgba(0, 73, 135, 0.40)"
                className="h-14 text-[18px] w-full"
                wrapperClass={`${comment ? "mr-0" : "mr-2"}`}
                onChangeText={setComment}
                value={comment}
              />
            </View>
            {comment && (
              <Pressable
                actionTag="create comment"
                onPress={handleCreateComment}
                className="bg-primary w-[50px] h-[50px] justify-center items-center rounded-lg ml-1"
                disabled={createCommentStatus === "loading"}
                style={{ backgroundColor: comment ? "#004987" : "#ccc" }}
              >
                {createCommentStatus === "loading" ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Rocket />
                )}
              </Pressable>
            )}
            {!comment && (
              <Camera
                onPress={() => Alert.alert("Coming Soon")}
                className="mr-2"
              />
            )}
            {!comment && (
              <Speak
                onPress={() => Alert.alert("Coming Soon")}
                className="ml-2"
              />
            )}
          </View>
        </View>
      </Screen>
      <InfoModal
        visible={deleteModalVisible}
        label="delete comment"
        description="Are you sure you want to delete this comment? This action cannot be undone."
        onRequestClose={setDeleteModalVisible}
        actionText="Yes"
        showLogo={true}
        handleAction={handleDeleteComment}
        secondaryText="No"
      />
    </KeyboardAvoidingView>
  );
};

export default PostOrGroupPostDetailsScreen;
