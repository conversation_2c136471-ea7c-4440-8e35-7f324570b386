import { z } from 'zod';
import { zString } from '.';
import { ReportPostStatus } from '../types/Connect';

const postTypeValidator = z.enum(['CONNECT', 'PRIVATE', 'PUBLIC']);

export const createConnectPostValidator = z.object({
  title: z.string().optional(),
  image: z.string().optional(),
  rabbleGroup: zString.optional(),
  postType: postTypeValidator,
});

export const editConnectPostValidator = z.object({
  title: z.string().optional(),
  postId: z.string(),
  image: z.string().optional(),
  rabbleGroup: zString.optional(),
  postType: postTypeValidator.optional(),
  disabled: z.boolean().optional(),
});

export const getPostsValidator = z.object({
  limit: z.number().min(1).max(100),
  cursor: z.number(), // <-- "cursor" needs to exist, but can be any type

  user: zString.optional(),
  postId: zString.optional(),
  postType: postTypeValidator.or(z.array(postTypeValidator)).optional(),
  rabbleGroup: zString.or(z.array(zString)).optional(),
  mergeGroupPosts: z.boolean().optional(),
});

export const getPostValidator = zString;

export const getReportPostsValidator = z.object({
  status: z.nativeEnum(ReportPostStatus).default(ReportPostStatus.PENDING),
});
export const reportPostValidator = zString;
export const deletePostValidator = zString;
export const manageReportPostValidator = z.object({
  reportId: zString,
  status: z.nativeEnum(ReportPostStatus),
});

export const likeValidator = z.object({
  post: zString,
  status: z.boolean(),
  reactionType: z.string().optional(),
});

export const createCommentValidator = z.object({
  comment: zString,
  post: zString,
  repliedTo: zString.optional(),
});

export const getCommentValidator = z.object({
  post: zString,
});

export const reportCommentValidator = z.object({ comment: zString });

export const deleteCommentValidator = zString;

export const getPostsOutPut = z.object({
  pagination: z.object({
    currentPage: z.number(),
    nextCursor: z.number().optional(),
    prevCursor: z.number().optional(),
    isLastPage: z.boolean(),
    totalPages: z.number(),
    totalItems: z.number(),
  }),
  posts: z.array(
    z.object({
      _id: zString,
      title: zString,
      image: zString.optional(),
      user: z
        .array(
          z.object({
            _id: zString,
            email: zString,
            firstname: zString,
            lastname: zString,
            username: zString,
            isCaregiver: z.boolean().optional(),
            profilePicture: zString.optional(),
          })
        )
        .optional(),
      rabbleGroup: z
        .array(
          z.object({
            groupName: zString.optional(),
            image: zString.optional(),
            privacy: z.enum(['public', 'private']),
            _id: zString,
            createdBy: zString,
          })
        )
        .optional(),
      postType: postTypeValidator,
      createdAt: zString,
      updatedAt: zString,
      recentLikes: z
        .array(
          z.object({
            _id: zString,
            post: zString,
            user: zString,
            createdAt: zString,
            updatedAt: zString,
            userDocument: z
              .array(
                z.object({
                  _id: zString,
                  email: zString,
                  firstname: zString,
                  lastname: zString,
                  username: zString,
                  profilePicture: zString.optional(),
                })
              )
              .optional(),
          })
        )
        .optional(),
      totalLikes: z.number(),
      isLiked: z.boolean(),
      reactionType: z.string().optional(),
    })
  ),
});

export const likeCommentValidator = z.object({
  comment: zString,
  status: z.boolean(),
  reactionType: z.string().optional(),
});
