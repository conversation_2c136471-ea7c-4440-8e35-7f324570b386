/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { addSeconds } from "date-fns";
import _ from "lodash";
import OtpModel, { OTP_TYPES } from "../models/Otp";
import UserAccountModel, { UserProfileModel } from "../models/UserAccount";
import {
  generateOtp,
  removeFileFromS3,
  sendBulkEmail,
  sendEmail,
  toObjectId,
} from "../utils";
import {
  createCaregiverValidator,
  createUpdateUserOrPatientProfileValidator,
  generateLoginOtpAndCreateUserValidator,
  loginValidator,
  requestUpdateUserOrPatientEmailPhoneOtpValidator,
  updateUserOrPatientEmailValidator,
  updateUserOrPatientPhoneValidator,
  updateUserOrPatientPartialValidator,
  userFilterValidator,
  validateUserOrPatientUpdateEmailPhoneOtpValidator,
  requestPatientToBeCareGiverValidator,
  updateUserFullProfileValidator,
  updateUserEventsValidator,
  userOrPhoneFilterValidator,
  bulkSmsDataValidator,
} from "../../../shared/validators/user.validator";
import { creatJwtToken } from "../utils/jwt";
import { sendBulkMessages, sendMessage } from "../utils/twilio";
import { MID } from "../@types/common";
import { createNotification } from "./notification.controller";
import { NotificationTypes } from "../../../shared/enums/notification";
import { CaregiverApprovalStatus } from "../../../shared/enums/user";
import { createUserRegistrationEvent } from "../utils/pinpoint-events";

import log from "../utils/logger";
import mixpanel, { MIXPANEL_TOKEN, MIXPANEL_TOKEN_V2 } from "../utils/mixpanel";
import {
  checkDuplicateUserByEmailContactUsername,
  checkDuplicateUserByPhoneAndEmail,
  checkDuplicateUserByQuery,
} from "../utils/duplicateUserConstraints";
import { CommentModel, LikeModel, PostModel } from "../models/Connect";
import { RabbleGroupModel, RabbleGroupUserModel } from "../models/RabbleGroup";
import { inviteSignupRabble } from "./rabblegroup.controller";

export const generateLoginOtpAndCreateUser = async ({
  email,
  phone,
  deviceId,
  isLogin = false,
}: z.infer<typeof generateLoginOtpAndCreateUserValidator>) => {
  const otp = await generateOtp();
  console.log({ deviceId, otp });
  console.log(
    `*** | deviceID: ${deviceId} | Phone: ${phone} | Email: ${email} | otp:${otp}`
  );

  let user = await UserAccountModel.findOne(
    deviceId
      ? { deviceId }
      : phone
      ? { "contact.phone": phone }
      : email
      ? { email }
      : {}
  ).sort({
    createdAt: -1,
  });
  if (isLogin && _.isEmpty(user)) {
    throw new TRPCError({ code: "NOT_FOUND", message: "user not found." });
  }
  if (deviceId && !email && !phone) {
    user = null;
  }

  if ((email || phone) && !isLogin && deviceId) {
    const filter = email ? { email } : phone ? { "contact.phone": phone } : {};
    const existingUser = await UserAccountModel.findOne(filter);
    console.log({ email, filter });
    if (_.size(existingUser) && existingUser?.isOnboardingCompleted) {
      throw new TRPCError({ code: "CONFLICT", message: "user already exist." });
    }
  }

  if (!user) {
    const queryConditions = [];
    if (phone) queryConditions.push({ "contact.phone": phone });
    if (email) queryConditions.push({ email });

    let userExists = queryConditions.length > 0;

    if (userExists) {
      const query = { $or: queryConditions };

      const update = {
        $setOnInsert: { role: "USER" },
        ...(phone && { "contact.phone": phone }),
        ...(email && { email }),
        ...(deviceId && { deviceId }),
      };

      const options = {
        new: true,
        upsert: true,
        setDefaultsOnInsert: true,
      };

      user = await UserAccountModel.findOneAndUpdate(query, update, options);

      if (!user) {
        user = await UserAccountModel.findOne(query);
      }

      if (!user) {
        throw new Error("User creation failed unexpectedly.");
      }

      console.log("User upserted:", user);
    } else {
      // No email or phone provided -> Create a new user with only `deviceId`
      user = new UserAccountModel({
        role: "USER",
        ...(deviceId && { deviceId }),
      });

      await user.save();

      console.log("User created with only deviceId:", user);
    }
  }

  // Now `user` is guaranteed to exist
  await OtpModel.updateOne(
    { user: user._id, type: OTP_TYPES.login },
    {
      /* update fields */
    },
    { upsert: true }
  );

  // create an user flow
  await OtpModel.updateOne(
    { user: user._id, type: OTP_TYPES.login },
    {
      otp,
      type: OTP_TYPES.login,
      user: user._id,
      expiresAt: addSeconds(new Date(), 300),
    },
    { upsert: true, new: true }
  );

  if (deviceId) {
    if (email || phone) {
      const updated = await UserAccountModel.updateOne(
        { _id: user._id },
        { email, contact: { phone } }
      );
      console.log(updated);
    } else {
      return { otp };
    }
  }

  // send otp to email/phone
  const isPhone = !!phone;
  if (isPhone)
    sendMessage({
      message: `Dear Customer , Your myRabble Magic Code is  ${otp}.`,
      to: `${process.env.COUNTRY_CODE || "+1"}${phone}`,
    });
  else
    sendEmail({
      to: email,
      text: `Dear Customer , Your myRabble Magic Code is  ${otp}.`,
      subject: `Dear Customer , Your myRabble Magic Code is ${otp}.`,
    });

  return user.toJSON();
};

const storeEmails = ["<EMAIL>", "<EMAIL>"];
// TODO: fix, filter with user ID
export const validateLoginOtp = async ({
  email,
  phone,
  deviceId,
  otp,
}: z.infer<typeof loginValidator>) => {
  const user = await UserAccountModel.findOne(
    deviceId
      ? { deviceId }
      : phone
      ? { "contact.phone": phone }
      : email
      ? { email }
      : {}
  ).sort({
    createdAt: -1,
  });

  console.log(
    `*** UserID${user?._id} | DeviceID: ${deviceId} | Phone: ${phone} | Email: ${email} | otp:${otp}`
  );

  if (!user)
    throw new TRPCError({ code: "NOT_FOUND", message: "user not found" });

  const _otp = await OtpModel.findOne({ otp, type: "login", user: user._id });

  console.log("***", otp, _otp, user._id);
  if (_otp?.otp !== otp && !storeEmails.includes(email || ""))
    throw new TRPCError({ code: "FORBIDDEN", message: "wrong magic code" });

  const token = creatJwtToken(user.toJSON()) as unknown as string;

  await createUserRegistrationEvent(user);

  return { ...user.toJSON(), token };
};

export const requestUpdateEmailPhoneOtp = async (
  {
    email,
    phone,
    updateType,
    userOrPatientId,
  }: z.infer<typeof requestUpdateUserOrPatientEmailPhoneOtpValidator>,
  userId: string
) => {
  const user = await UserAccountModel.findById(userOrPatientId);
  if (!user)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "user not found in database",
    });
  if (
    !(
      userId === userOrPatientId ||
      user.managedBy?.find((u) => u.user.toString() === userId)
    )
  )
    throw new TRPCError({ code: "UNAUTHORIZED", message: "UNAUTHORIZED" });

  const otp = await generateOtp();
  await checkDuplicateUserByPhoneAndEmail(email, phone, userId);

  // send otp to email
  if (updateType === "phone")
    sendMessage({
      message: `Dear Customer , Your myRabble Magic Code is  ${otp}.`,
      to: `${process.env.COUNTRY_CODE || "+1"}${phone}`,
    });
  if (updateType === "email" && email)
    sendEmail({
      to: email,
      text: `Dear Customer , Your myRabble Magic Code is  ${otp}.`,
      subject: `Dear Customer , Your myRabble Magic Code is ${otp}.`,
    });

  // send otp to phone
  await OtpModel.updateOne(
    { user: userOrPatientId, type: OTP_TYPES["update-email-contact"] },
    {
      otp,
      type: OTP_TYPES["update-email-contact"],
      user: userOrPatientId,
      expiresAt: addSeconds(new Date(), 300),
    },
    { upsert: true, new: true }
  );
};
export const validateUpdateEmailPhoneOtp = async (
  {
    otp,
    userOrPatientId,
  }: z.infer<typeof validateUserOrPatientUpdateEmailPhoneOtpValidator>,
  userId: string
) => {
  const user = await UserAccountModel.findById(userOrPatientId);
  if (!user)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "user not found in database",
    });
  if (
    !(
      userId === userOrPatientId ||
      user.managedBy?.find((u) => u.user.toString() === userId)
    )
  )
    throw new TRPCError({ code: "UNAUTHORIZED", message: "UNAUTHORIZED" });

  const _otp = await OtpModel.findOne({
    otp,
    type: OTP_TYPES["update-email-contact"],
    user,
  });
  if (_otp?.otp !== otp)
    throw new TRPCError({ code: "FORBIDDEN", message: "wrong magic code" });
  return true;
};

export const userFilter = async ({
  username,
  includePatientsOnly,
}: z.infer<typeof userFilterValidator>) =>
  UserAccountModel.find({
    username: username.toLowerCase(),
    ...(includePatientsOnly
      ? {
          isCaregiver: { $in: [false, null] },
        }
      : {}),
  });

export const userEmailOrPhoneFilter = async ({
  email,
  phone,
}: z.infer<typeof userOrPhoneFilterValidator>) =>
  UserAccountModel.find(
    email
      ? {
          email,
        }
      : {
          "contact.phone": phone,
        }
  );

export const updateUserEvents = async (
  payload: z.infer<typeof updateUserEventsValidator>,
  userId: string
) => {
  const user = await UserAccountModel.findById(userId);
  if (!user)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "user not found in database",
    });
  await createUserRegistrationEvent(user, {
    visitedAgainAfterSignedIn: payload.visitedAgainAfterSignedIn,
  });
};

export const updateUserOrPatientPartial = async (
  payload: z.infer<typeof updateUserOrPatientPartialValidator>,
  userId: string
) => {
  const user = await UserAccountModel.findById(payload.userOrPatientId);
  if (!user)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "user not found in database",
    });
  if (
    !(
      userId === payload.userOrPatientId ||
      user.managedBy?.find((u) => u.user.toString() === userId)
    )
  )
    throw new TRPCError({ code: "UNAUTHORIZED", message: "UNAUTHORIZED" });

  if (!user)
    throw new TRPCError({ code: "NOT_FOUND", message: "user not found in db" });

  // check if user exists with email, phone or username
  await checkDuplicateUserByEmailContactUsername(payload, user);

  // check if user has updated new profile picture and old one also exists
  if (payload.profilePicture && user.profilePicture) {
    removeFileFromS3({ key: user.profilePicture }).catch((err) =>
      log.error("@FileRemoveError", err)
    );
  }

  // set managedBy to [] if switched to caregiver
  if (!user.isCaregiver && payload.isCaregiver) {
    await UserAccountModel.updateOne(
      { _id: user._id },
      { $set: { managedBy: [] } },
      { new: true }
    );
  }

  if (payload.isCaregiver) {
    const userObj = _.pick(user.toJSON(), [
      "firstname",
      "lastname",
      "contact",
      "email",
      "username",
      "marketingConsent",
      "isCaregiver",
    ]);

    await createUserRegistrationEvent(
      { ...userObj, ...payload },
      { profileCompleted: true }
    );
  }

  await user.updateOne(payload);
  mixpanel.createUserProfile({
    $distinct_id: String(payload.userOrPatientId),
    // @ts-ignore
    $set: _.mapValues(payload, String),
    $token: MIXPANEL_TOKEN,
  });
  console.log("MIXPANEL_TOKEN_V2", MIXPANEL_TOKEN_V2);
  mixpanel.createUserProfile({
    $distinct_id: String(payload.userOrPatientId),
    // @ts-expect-error
    $set: _.mapValues(payload, String),
    $token: MIXPANEL_TOKEN_V2 || "",
  });
  return user.toJSON();
};

export const updateUserOrPatientEmail = async (
  payload: z.infer<typeof updateUserOrPatientEmailValidator>,
  userId: MID
) => {
  const user = await UserAccountModel.findById(payload.userOrPatientId);
  if (!user)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "user not found in database",
    });
  if (
    !(
      userId === payload.userOrPatientId ||
      user.managedBy?.find((u) => u.user.toString() === userId)
    )
  )
    throw new TRPCError({ code: "UNAUTHORIZED", message: "UNAUTHORIZED" });

  // check if user exists with email, phone or username
  await checkDuplicateUserByQuery(payload, user);
  return user.updateOne(payload);
};

export const updateUserOrPatientPhone = async (
  payload: z.infer<typeof updateUserOrPatientPhoneValidator>,
  userId: MID
) => {
  const user = await UserAccountModel.findById(payload.userOrPatientId);
  if (!user)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "user not found in database",
    });
  if (
    !(
      userId === payload.userOrPatientId ||
      user.managedBy?.find((u) => u.user.toString() === userId)
    )
  )
    throw new TRPCError({ code: "UNAUTHORIZED", message: "UNAUTHORIZED" });

  // check if user exists with email, phone or username
  await checkDuplicateUserByQuery(payload, user, "phone already in use");
  return user.updateOne(payload);
};

export const createUpdateUserOrPatientProfile = async (
  payload: z.infer<typeof createUpdateUserOrPatientProfileValidator>,
  userId: string
) => {
  const user = await UserAccountModel.findById(payload.userOrPatientId);
  if (!user)
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "user not found in database",
    });
  if (
    !(
      userId === payload.userOrPatientId ||
      user.managedBy?.find((u) => u.user.toString() === userId)
    )
  )
    throw new TRPCError({ code: "UNAUTHORIZED", message: "UNAUTHORIZED" });

  return UserProfileModel.updateOne(
    { user: payload.userOrPatientId },
    payload,
    { upsert: true, new: true }
  );
};

export const updateUserFullProfile = async (
  payload: z.infer<typeof updateUserFullProfileValidator>,
  userId: MID
) => {
  const { personalProfile, userProfile, userOrPatientId } = payload;
  await createUserRegistrationEvent(
    { ...userProfile, userOrPatientId },
    { profileCompleted: true }
  );
  await updateUserOrPatientPartial(
    { ...userProfile, userOrPatientId },
    userId.toString()
  );
  await createUpdateUserOrPatientProfile(
    { ...personalProfile, userOrPatientId },
    userId.toString()
  );
  mixpanel.createUserProfile({
    $distinct_id: String(userOrPatientId),
    // @ts-ignore
    $set: _.mapValues({ ...personalProfile, ...userProfile }, String),
    $token: MIXPANEL_TOKEN,
  });
};

// Caregiver
export const createCareGiver = async (
  payload: z.infer<typeof createCaregiverValidator>,
  user: MID
) => {
  const {
    address,
    personal,
    contact,
    email,
    firstname,
    lastname,
    marketingConsent,
    tncConsent,
    username,
  } = payload;
  await checkDuplicateUserByEmailContactUsername(payload);

  const userDoc = await new UserAccountModel({
    contact,
    email,
    firstname,
    lastname,
    managedBy: [
      {
        accountType: "caregiver",
        user,
        approvalStatus: CaregiverApprovalStatus.APPROVED,
      },
    ],
    marketingConsent,
    tncConsent,
    privacyConsent: true,
    username,
    role: "USER",
  }).save();

  await new UserProfileModel({ address, personal, user: userDoc._id }).save();

  return userDoc.toJSON();
};

export const getCareGiverPatients = (userId: string) =>
  UserAccountModel.find({ "managedBy.user": userId }).lean();

export const requestPatientToBeCareGiver = async (
  {
    username,
    relationship,
  }: z.infer<typeof requestPatientToBeCareGiverValidator>,
  userId: MID
) => {
  const patient = await UserAccountModel.findOne({
    username: username.toLowerCase(),
  });
  if (!patient) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "patient not found with specified username",
    });
  }
  await createNotification({
    notificationType: NotificationTypes.CAREGIVER_APPROVAL,
    content: "",
    userId: patient._id.toString(),
    requestedUserId: userId.toString(),
    metadata: {},
  });

  const managedByEntry = patient.managedBy?.find(
    (entry) => entry.user.toString() === userId.toString()
  );

  if (managedByEntry) {
    // Update the existing entry
    managedByEntry.accountType = "caregiver";
    managedByEntry.relationship = relationship;
    managedByEntry.approvalStatus = CaregiverApprovalStatus.PENDING_APPROVAL;
  } else {
    // Add a new entry
    patient.managedBy = patient.managedBy || [];
    patient.managedBy.push({
      user: userId.toString(),
      accountType: "caregiver",
      relationship,
      approvalStatus: CaregiverApprovalStatus.PENDING_APPROVAL,
    });
  }

  await patient.save();
};

export const updateCaregiverApprovalStatus = async (
  patientId: MID,
  caregiverId: MID,
  status: CaregiverApprovalStatus
) => {
  const result = await UserAccountModel.updateOne(
    { _id: patientId, "managedBy.user": caregiverId },
    { $set: { "managedBy.$.approvalStatus": status } }
  );
  if (!result.modifiedCount) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "patient and caregiver relationship not found",
    });
  }
};

export const revokeCareGiverAccess = async (
  caregiverId: MID,
  patientId: MID
) => {
  const result = await UserAccountModel.updateOne(
    { _id: patientId, "managedBy.user": caregiverId },
    { $pull: { managedBy: { user: caregiverId } } }
  );
  if (!result.modifiedCount) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "patient and caregiver relationship not found",
    });
  }
};

export const deleteUserData = async (userId: MID) => {
  await Promise.all([
    UserAccountModel.findByIdAndDelete(userId),
    UserProfileModel.deleteMany({ user: userId }),
    PostModel.deleteMany({ user: userId }),
    CommentModel.deleteMany({ user: userId }),
    LikeModel.deleteMany({ user: userId }),
    RabbleGroupModel.deleteMany({ $or: [{ createdBy: userId }] }),
    RabbleGroupUserModel.deleteMany({ user: userId }),
  ]);
  return { status: "OK" };
};

export const sendConnectOnboardingInvitations = async (
  { message, recipients, groupId }: z.infer<typeof bulkSmsDataValidator>,
  userId: MID
) => {
  // Filter out recipients with no contact information
  const validRecipients = recipients.filter(
    (recipient) => recipient.email || recipient.phoneNumbers
  );

  // Process emails
  const emailRecipients: string[] = validRecipients
    .filter((recipient) => recipient.email)
    .map((r) => r.email || "");

  // Process phone numbers
  const smsRecipients: string[] = validRecipients
    .filter((recipient) => recipient.phoneNumbers)
    .map((r) => r.phoneNumbers || "");

  // Send messages
  if (smsRecipients.length > 0) {
    await sendBulkMessages({ message, recipients: smsRecipients });
  }

  // Send emails
  if (emailRecipients.length > 0) {
    await sendBulkEmail(
      emailRecipients,
      "Invitation to join myRabble",
      message
    );
  }

  // Create valid invitation objects (only for recipients with either email or phone)
  const invitations = validRecipients.map((recipient) => ({
    email: recipient.email || null,
    phone: recipient.phoneNumbers || null,
    rabbleGroup: groupId ? toObjectId(groupId) : null,
    invitedBy: userId,
  }));

  // Only call inviteSignupRabble if there are valid invitations
  if (invitations.length > 0) {
    console.log("Valid invitations:", invitations);
    await inviteSignupRabble({ invitations }, userId);
  } else {
    console.log("No valid invitations to send");
  }

  return null;
};
