import { Comment, Post } from '../../../shared/types/Connect';
import { UserAccount } from '../../../shared/types/user';
import {
  createCommentValidator,
  createConnectPostValidator,
  deleteCommentValidator,
  deletePostValidator,
  manageReportPostValidator,
  getCommentValidator,
  getPostValidator,
  getPostsValidator,
  likeCommentValidator,
  likeValidator,
  reportCommentValidator,
  reportPostValidator,
  getReportPostsValidator,
  editConnectPostValidator,
} from '../../../shared/validators/connect.validator';
import {
  createComment,
  createPost,
  deleteComment,
  deletePost,
  editPost,
  getComments,
  getPost,
  getPosts,
  likeComment,
  likePost,
  manageReportPost,
  reportComment,
  reportPost,
} from '../controller/connect.controller';
import { authProcedure } from '../middleware/auth';
import { ReportPostModel } from '../models/Connect';
import t from '../startup/trpc';

export const connectRouter = t.router({
  createPost: authProcedure()
    .input(createConnectPostValidator)
    .mutation(({ input, ctx }) => createPost(input, ctx.user._id)),

  editPost: authProcedure()
    .input(editConnectPostValidator)
    .mutation(({ input, ctx }) => editPost(input, ctx.user._id)),

  getPosts: t.procedure.input(getPostsValidator).query(({ input }) => getPosts(input)),

  getPost: authProcedure()
    .input(getPostValidator)
    .query(({ input }) => getPost(input)),

  deletePost: authProcedure()
    .input(deletePostValidator)
    .mutation(({ input }) => deletePost(input)),

  reportPost: authProcedure()
    .input(reportPostValidator)
    .mutation(({ input, ctx }) => reportPost(input, ctx.user._id)),

  getReportedPosts: authProcedure()
    .input(getReportPostsValidator)
    .query(async ({ input: { status } }) => {
      const res = await ReportPostModel.find({ status })
        .populate<{
          post: Post & { user?: { email: string; firstname: string; lastname: string; username: string; contact?: { phone: string } } };
          comment: Comment;
          reportedBy: UserAccount;
        }>([
          {
            path: 'post',
            populate: [{ path: 'user' }],
          },
          {
            path: 'comment',
          },
          {
            path: 'reportedBy',
          },
        ])
        .lean();

      return res;
    }),

  likePost: authProcedure()
    .input(likeValidator)
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    .mutation(({ input, ctx }) => likePost(input, ctx.user._id)),

  getComments: authProcedure()
    .input(getCommentValidator)
    .query(({ input, ctx }) => getComments(input, ctx.user._id)),

  createComment: authProcedure()
    .input(createCommentValidator)
    .mutation(({ ctx, input }) => createComment(input, ctx.user._id)),

  deleteComment: authProcedure()
    .input(deleteCommentValidator)
    .mutation(({ ctx, input }) => deleteComment(input, ctx.user._id)),

  reportComment: authProcedure()
    .input(reportCommentValidator)
    .mutation(({ ctx, input }) => reportComment(input, ctx.user._id)),

  manageReportPost: authProcedure()
    .input(manageReportPostValidator)
    .mutation(({ input }) => manageReportPost(input)),

  likeComment: authProcedure()
    .input(likeCommentValidator)
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    .mutation(({ ctx, input }) => likeComment(input, ctx.user._id)),
});
