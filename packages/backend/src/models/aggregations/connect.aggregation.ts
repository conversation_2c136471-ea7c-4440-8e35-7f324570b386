import mongoose from 'mongoose';
import { z } from 'zod';
import { getPostsValidator } from '../../../../shared/validators/connect.validator';

export const getPostsAggregation = ({ cursor, limit, postId, postType, rabbleGroup, user, mergeGroupPosts }: z.infer<typeof getPostsValidator>) => [
  {
    $facet: {
      // get posts
      posts: [
        {
          $match: {
            disabled: { $ne: true },
            $or: [
              {
                ...(postId && { _id: new mongoose.Types.ObjectId(postId) }),
                ...(postType && { postType: typeof postType === 'string' ? postType : { $in: postType } }),
                ...(rabbleGroup &&
                  !mergeGroupPosts && {
                    rabbleGroup:
                      typeof rabbleGroup === 'string'
                        ? new mongoose.Types.ObjectId(rabbleGroup)
                        : { $in: rabbleGroup.map((_) => new mongoose.Types.ObjectId(_)) },
                  }),
              },
              ...(mergeGroupPosts && rabbleGroup
                ? [
                    {
                      rabbleGroup:
                        typeof rabbleGroup === 'string'
                          ? new mongoose.Types.ObjectId(rabbleGroup)
                          : { $in: rabbleGroup.map((_) => new mongoose.Types.ObjectId(_)) },
                    },
                  ]
                : []),
            ],
          },
        },
        {
          $sort: {
            createdAt: -1,
          },
        },
        {
          $skip: cursor * limit,
        },
        {
          $limit: limit,
        },
        {
          $lookup: {
            from: 'user-accounts',
            localField: 'user',
            foreignField: '_id',
            as: 'user',
          },
        },
        {
          $lookup: {
            from: 'rabble-groups',
            localField: 'rabbleGroup',
            foreignField: '_id',
            as: 'rabbleGroup',
          },
        },
        {
          $lookup: {
            from: 'likes',
            localField: '_id',
            foreignField: 'post',
            as: 'recentLikes',
            pipeline: [
              {
                $sort: {
                  createdAt: -1,
                },
              },
              {
                $lookup: {
                  from: 'user-accounts',
                  localField: 'user',
                  foreignField: '_id',
                  as: 'userDocument',
                },
              },
            ],
          },
        },
        {
          $addFields: {
            totalLikes: {
              $size: '$recentLikes',
            },
            recentLikes: {
              $slice: ['$recentLikes', 3],
            },
          },
        },
        {
          $addFields: {
            isLiked: {
              $in: [
                new mongoose.Types.ObjectId(user),
                {
                  $ifNull: ['$recentLikes.user', []],
                },
              ],
            },
          },
        },
        {
          $addFields: {
            reactionType: {
              $first: {
                $map: {
                  input: {
                    $filter: {
                      input: '$recentLikes',
                      as: 'like',
                      cond: {
                        $eq: ['$$like.user', new mongoose.Types.ObjectId(user)],
                      },
                    },
                  },
                  as: 'match',
                  in: '$$match.reactionType',
                },
              },
            },
          },
        }
        
      ],

      totalPostsCount: [
        {
          $match: {
            disabled: { $ne: true },
          },
        },
        { $count: 'totalCount' },
      ],
    },
  },
];

export const getUserJoinedGroupsAggregation = ({
  userId,
  showUserCreatedGroups = false,
}: {
  userId: string | string[];
  showUserCreatedGroups?: boolean;
}) => {
  const users = typeof userId === 'string' ? [userId] : userId;

  return [
    {
      $match: {
        user: { $in: users.map((_) => new mongoose.Types.ObjectId(_)) },
        ...(!showUserCreatedGroups && { role: { $ne: 'owner' } }),
      },
    },
    {
      $lookup: {
        from: 'rabble-groups',
        localField: 'rabbleGroup',
        foreignField: '_id',
        as: 'rabbleGroup',
        pipeline: [
          {
            $match: {
              deleted: {
                $ne: true,
              },
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$rabbleGroup',
      },
    },
  ];
};
